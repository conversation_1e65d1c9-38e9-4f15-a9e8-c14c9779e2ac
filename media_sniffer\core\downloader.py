"""
下载管理器
==========

支持多线程下载和断点续传的媒体下载管理器
"""

import os
import asyncio
import aiohttp
import aiofiles
import logging
from typing import List, Dict, Optional, Callable, Any
from pathlib import Path
from urllib.parse import urlparse, unquote
import time
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm
from .parser import MediaInfo
from ..utils.media_types import MediaType

class DownloadProgress:
    """下载进度信息"""
    
    def __init__(self, url: str, filename: str, total_size: int = 0):
        self.url = url
        self.filename = filename
        self.total_size = total_size
        self.downloaded_size = 0
        self.start_time = time.time()
        self.status = "pending"  # pending, downloading, completed, failed, paused
        self.error_message = ""
        self.speed = 0
    
    @property
    def progress_percent(self) -> float:
        """下载进度百分比"""
        if self.total_size > 0:
            return (self.downloaded_size / self.total_size) * 100
        return 0
    
    @property
    def elapsed_time(self) -> float:
        """已用时间"""
        return time.time() - self.start_time
    
    @property
    def eta(self) -> float:
        """预计剩余时间"""
        if self.speed > 0 and self.total_size > 0:
            remaining_bytes = self.total_size - self.downloaded_size
            return remaining_bytes / self.speed
        return 0

class MediaDownloader:
    """媒体下载管理器"""
    
    def __init__(self, config=None):
        """初始化下载管理器"""
        self.config = config
        self.download_path = Path(config.download.download_path if config else "./downloads")
        self.download_path.mkdir(parents=True, exist_ok=True)
        
        self.active_downloads: Dict[str, DownloadProgress] = {}
        self.completed_downloads: List[DownloadProgress] = []
        self.failed_downloads: List[DownloadProgress] = []
        
        # 下载配置
        self.max_concurrent = config.network.max_concurrent_downloads if config else 5
        self.chunk_size = config.network.chunk_size if config else 8192
        self.max_file_size = config.download.max_file_size if config else 1024*1024*1024  # 1GB
        
        # 进度回调
        self.progress_callback: Optional[Callable] = None
    
    def set_progress_callback(self, callback: Callable[[DownloadProgress], None]) -> None:
        """设置进度回调函数"""
        self.progress_callback = callback
    
    async def download_media_list(self, media_list: List[MediaInfo], 
                                 create_subdirs: bool = True) -> Dict[str, Any]:
        """下载媒体列表"""
        results = {
            'successful': [],
            'failed': [],
            'skipped': []
        }
        
        # 过滤和验证媒体列表
        valid_media = self._filter_media_list(media_list)
        
        if not valid_media:
            logging.warning("没有有效的媒体文件需要下载")
            return results
        
        logging.info(f"开始下载 {len(valid_media)} 个媒体文件")
        
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        # 创建下载任务
        tasks = []
        for media in valid_media:
            task = self._download_single_media(media, semaphore, create_subdirs)
            tasks.append(task)
        
        # 执行下载
        download_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(download_results):
            media = valid_media[i]
            if isinstance(result, Exception):
                results['failed'].append({
                    'media': media,
                    'error': str(result)
                })
                logging.error(f"下载失败: {media.url} - {result}")
            elif result:
                results['successful'].append({
                    'media': media,
                    'filepath': result
                })
                logging.info(f"下载成功: {result}")
            else:
                results['skipped'].append({
                    'media': media,
                    'reason': '下载被跳过'
                })
        
        logging.info(f"下载完成 - 成功: {len(results['successful'])}, "
                    f"失败: {len(results['failed'])}, 跳过: {len(results['skipped'])}")
        
        return results
    
    async def _download_single_media(self, media: MediaInfo, semaphore: asyncio.Semaphore,
                                   create_subdirs: bool = True) -> Optional[str]:
        """下载单个媒体文件"""
        async with semaphore:
            try:
                # 生成文件路径
                filepath = self._generate_filepath(media, create_subdirs)
                
                # 检查文件是否已存在
                if filepath.exists() and not (self.config and self.config.download.overwrite_existing):
                    logging.info(f"文件已存在，跳过下载: {filepath}")
                    return str(filepath)
                
                # 创建进度对象
                progress = DownloadProgress(media.url, filepath.name)
                self.active_downloads[media.url] = progress
                
                # 开始下载
                progress.status = "downloading"
                result = await self._download_file(media.url, filepath, progress)
                
                # 更新状态
                if result:
                    progress.status = "completed"
                    self.completed_downloads.append(progress)
                    return str(filepath)
                else:
                    progress.status = "failed"
                    self.failed_downloads.append(progress)
                    return None
                
            except Exception as e:
                logging.error(f"下载异常: {media.url} - {e}")
                if media.url in self.active_downloads:
                    self.active_downloads[media.url].status = "failed"
                    self.active_downloads[media.url].error_message = str(e)
                return None
            finally:
                # 清理活动下载记录
                if media.url in self.active_downloads:
                    del self.active_downloads[media.url]
    
    async def _download_file(self, url: str, filepath: Path, progress: DownloadProgress) -> bool:
        """下载文件"""
        try:
            # 创建目录
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # 检查是否支持断点续传
            resume_pos = 0
            if filepath.exists():
                resume_pos = filepath.stat().st_size
                progress.downloaded_size = resume_pos
            
            # 设置请求头
            headers = {}
            if resume_pos > 0:
                headers['Range'] = f'bytes={resume_pos}-'
            
            # 设置超时
            timeout = aiohttp.ClientTimeout(
                total=self.config.network.timeout if self.config else 300,
                connect=30
            )
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, headers=headers) as response:
                    # 检查响应状态
                    if response.status not in [200, 206]:
                        logging.error(f"HTTP错误: {response.status} - {url}")
                        return False
                    
                    # 获取文件大小
                    content_length = response.headers.get('content-length')
                    if content_length:
                        if resume_pos > 0 and response.status == 206:
                            progress.total_size = resume_pos + int(content_length)
                        else:
                            progress.total_size = int(content_length)
                    
                    # 检查文件大小限制
                    if progress.total_size > self.max_file_size:
                        logging.warning(f"文件过大，跳过下载: {url} ({progress.total_size} bytes)")
                        return False
                    
                    # 打开文件进行写入
                    mode = 'ab' if resume_pos > 0 else 'wb'
                    async with aiofiles.open(filepath, mode) as f:
                        last_update_time = time.time()
                        
                        async for chunk in response.content.iter_chunked(self.chunk_size):
                            await f.write(chunk)
                            progress.downloaded_size += len(chunk)
                            
                            # 更新速度和进度
                            current_time = time.time()
                            if current_time - last_update_time >= 1.0:  # 每秒更新一次
                                time_diff = current_time - last_update_time
                                progress.speed = len(chunk) / time_diff
                                last_update_time = current_time
                                
                                # 调用进度回调
                                if self.progress_callback:
                                    self.progress_callback(progress)
            
            logging.info(f"文件下载完成: {filepath}")
            return True
            
        except asyncio.TimeoutError:
            logging.error(f"下载超时: {url}")
            return False
        except Exception as e:
            logging.error(f"下载文件异常: {url} - {e}")
            return False
    
    def _filter_media_list(self, media_list: List[MediaInfo]) -> List[MediaInfo]:
        """过滤媒体列表"""
        if not self.config or not self.config.download.enable_download:
            return []
        
        valid_media = []
        allowed_types = self.config.download.allowed_types
        
        for media in media_list:
            # 检查类型过滤
            if allowed_types and media.media_type.value not in allowed_types:
                continue
            
            # 检查URL有效性
            if not media.url or not media.url.startswith(('http://', 'https://')):
                continue
            
            valid_media.append(media)
        
        return valid_media
    
    def _generate_filepath(self, media: MediaInfo, create_subdirs: bool = True) -> Path:
        """生成文件路径"""
        # 从URL提取文件名
        parsed_url = urlparse(media.url)
        filename = unquote(parsed_url.path.split('/')[-1])
        
        # 如果没有文件名，使用标题或生成一个
        if not filename or '.' not in filename:
            if media.title:
                # 清理标题中的非法字符
                clean_title = self._clean_filename(media.title)
                # 根据媒体类型添加扩展名
                ext = self._get_extension_by_type(media.media_type)
                filename = f"{clean_title}.{ext}"
            else:
                # 生成默认文件名
                timestamp = int(time.time())
                ext = self._get_extension_by_type(media.media_type)
                filename = f"media_{timestamp}.{ext}"
        
        # 创建子目录
        if create_subdirs and self.config and self.config.download.create_subdirs:
            subdir = media.media_type.value
            filepath = self.download_path / subdir / filename
        else:
            filepath = self.download_path / filename
        
        # 处理文件名冲突
        counter = 1
        original_filepath = filepath
        while filepath.exists():
            stem = original_filepath.stem
            suffix = original_filepath.suffix
            filepath = original_filepath.parent / f"{stem}_{counter}{suffix}"
            counter += 1
        
        return filepath
    
    def _clean_filename(self, filename: str) -> str:
        """清理文件名中的非法字符"""
        # 移除或替换非法字符
        illegal_chars = '<>:"/\\|?*'
        for char in illegal_chars:
            filename = filename.replace(char, '_')
        
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100]
        
        return filename.strip()
    
    def _get_extension_by_type(self, media_type: MediaType) -> str:
        """根据媒体类型获取默认扩展名"""
        extensions = {
            MediaType.VIDEO: 'mp4',
            MediaType.AUDIO: 'mp3',
            MediaType.IMAGE: 'jpg'
        }
        return extensions.get(media_type, 'bin')
    
    def get_download_status(self) -> Dict[str, Any]:
        """获取下载状态"""
        return {
            'active_downloads': len(self.active_downloads),
            'completed_downloads': len(self.completed_downloads),
            'failed_downloads': len(self.failed_downloads),
            'active_details': list(self.active_downloads.values()),
            'download_path': str(self.download_path)
        }
    
    def cancel_download(self, url: str) -> bool:
        """取消下载"""
        if url in self.active_downloads:
            self.active_downloads[url].status = "cancelled"
            return True
        return False
    
    def clear_completed(self) -> None:
        """清理已完成的下载记录"""
        self.completed_downloads.clear()
        self.failed_downloads.clear()

# 同步下载接口
def download_media_sync(media_list: List[MediaInfo], config=None) -> Dict[str, Any]:
    """同步下载媒体列表"""
    downloader = MediaDownloader(config)
    
    # 运行异步下载
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        result = loop.run_until_complete(
            downloader.download_media_list(media_list)
        )
        return result
    finally:
        loop.close()
