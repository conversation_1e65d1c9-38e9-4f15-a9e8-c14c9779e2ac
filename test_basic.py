#!/usr/bin/env python3
"""
基础功能测试脚本
================

测试网页多媒体嗅探程序的基本功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from media_sniffer import MediaDetector, HTMLParser, MediaDownloader
        from media_sniffer.utils.config import Config
        from media_sniffer.utils.media_types import MediaTypes, MediaType
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_config():
    """测试配置管理"""
    print("\n测试配置管理...")
    
    try:
        from media_sniffer.utils.config import Config
        
        # 测试默认配置
        config = Config()
        print(f"✅ 默认配置加载成功")
        print(f"   网络超时: {config.network.timeout}秒")
        print(f"   启用JavaScript: {config.crawler.enable_javascript}")
        print(f"   下载路径: {config.download.download_path}")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_media_types():
    """测试媒体类型识别"""
    print("\n测试媒体类型识别...")
    
    try:
        from media_sniffer.utils.media_types import MediaTypes, MediaType
        
        # 测试URL类型识别
        test_urls = [
            "https://example.com/video.mp4",
            "https://example.com/audio.mp3", 
            "https://example.com/image.jpg",
            "https://youtube.com/watch?v=123",
            "https://example.com/unknown.txt"
        ]
        
        for url in test_urls:
            media_type = MediaTypes.get_media_type_by_extension(url)
            is_media = MediaTypes.is_media_url(url)
            is_video_site, site_name = MediaTypes.is_video_site_url(url)
            
            print(f"   {url}")
            print(f"     类型: {media_type.value}")
            print(f"     是媒体: {is_media}")
            if is_video_site:
                print(f"     视频网站: {site_name}")
        
        print("✅ 媒体类型识别测试成功")
        return True
    except Exception as e:
        print(f"❌ 媒体类型测试失败: {e}")
        return False

def test_html_parser():
    """测试HTML解析器"""
    print("\n测试HTML解析器...")
    
    try:
        from media_sniffer.core.parser import HTMLParser
        
        # 测试HTML内容
        test_html = """
        <html>
        <body>
            <video src="test.mp4" title="测试视频"></video>
            <audio src="test.mp3"></audio>
            <img src="test.jpg" alt="测试图片">
            <a href="video.avi">视频链接</a>
        </body>
        </html>
        """
        
        parser = HTMLParser()
        media_list = parser.parse_html(test_html, "https://example.com")
        
        print(f"✅ HTML解析成功，找到 {len(media_list)} 个媒体文件")
        for media in media_list:
            print(f"   {media.media_type.value}: {media.url}")
        
        return True
    except Exception as e:
        print(f"❌ HTML解析测试失败: {e}")
        return False

def test_detector_basic():
    """测试基础检测功能"""
    print("\n测试基础检测功能...")
    
    try:
        from media_sniffer import MediaDetector
        from media_sniffer.utils.config import Config
        
        # 创建配置（禁用下载和动态检测以避免网络依赖）
        config = Config()
        config.download.enable_download = False
        config.crawler.enable_javascript = False
        
        detector = MediaDetector(config)
        
        print("✅ 检测器创建成功")
        print(f"   配置文件: {config.config_file}")
        print(f"   下载功能: {'启用' if config.download.enable_download else '禁用'}")
        print(f"   动态检测: {'启用' if config.crawler.enable_javascript else '禁用'}")
        
        # 清理资源
        detector.close()
        
        return True
    except Exception as e:
        print(f"❌ 检测器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始基础功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_media_types,
        test_html_parser,
        test_detector_basic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础功能测试通过！")
        print("\n可以尝试运行以下命令测试完整功能:")
        print("python main.py --help")
        print("python main.py https://httpbin.org/html")
    else:
        print("⚠️  部分测试失败，请检查依赖安装")
        print("\n请确保已安装所有依赖:")
        print("pip install -r requirements.txt")

if __name__ == "__main__":
    main()
