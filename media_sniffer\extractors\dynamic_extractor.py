"""
动态内容提取器
==============

使用Selenium处理JavaScript动态加载的内容
"""

import time
import logging
from typing import List, Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from ..core.parser import HTMLParser, MediaInfo
from ..utils.anti_crawler import AntiCrawler

class DynamicExtractor:
    """动态内容提取器"""
    
    def __init__(self, config=None, browser='chrome'):
        """初始化动态提取器"""
        self.config = config
        self.browser = browser.lower()
        self.driver = None
        self.parser = HTMLParser(config)
        self.anti_crawler = AntiCrawler(config)
        
    def __enter__(self):
        """上下文管理器入口"""
        self._setup_driver()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
    
    def _setup_driver(self) -> None:
        """设置WebDriver"""
        try:
            if self.browser == 'chrome':
                options = self.anti_crawler.get_selenium_options('chrome')
                service = webdriver.ChromeService(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=options)
            
            elif self.browser == 'firefox':
                options = self.anti_crawler.get_selenium_options('firefox')
                service = webdriver.FirefoxService(GeckoDriverManager().install())
                self.driver = webdriver.Firefox(service=service, options=options)
            
            else:
                raise ValueError(f"不支持的浏览器类型: {self.browser}")
            
            # 应用反检测设置
            self.anti_crawler.setup_selenium_driver(self.driver)
            
            logging.info(f"WebDriver初始化成功: {self.browser}")
            
        except Exception as e:
            logging.error(f"WebDriver初始化失败: {e}")
            raise
    
    def extract_from_url(self, url: str, wait_time: int = None, 
                        scroll_to_bottom: bool = True) -> List[MediaInfo]:
        """从URL提取动态内容"""
        if not self.driver:
            self._setup_driver()
        
        try:
            logging.info(f"开始提取动态内容: {url}")
            
            # 加载页面
            self.driver.get(url)
            
            # 等待页面加载
            wait_time = wait_time or (self.config.crawler.page_load_timeout if self.config else 30)
            time.sleep(2)  # 基础等待
            
            # 等待特定元素加载
            self._wait_for_content_load()
            
            # 滚动页面以触发懒加载
            if scroll_to_bottom:
                self._scroll_and_wait()
            
            # 处理可能的弹窗或覆盖层
            self._handle_overlays()
            
            # 获取页面源码
            html_content = self.driver.page_source
            current_url = self.driver.current_url
            
            # 解析HTML内容
            media_list = self.parser.parse_html(html_content, current_url)
            
            # 提取动态加载的媒体
            dynamic_media = self._extract_dynamic_media()
            media_list.extend(dynamic_media)
            
            # 去重
            unique_media = self._deduplicate_media(media_list)
            
            logging.info(f"动态提取完成，找到 {len(unique_media)} 个媒体文件")
            return unique_media
            
        except Exception as e:
            logging.error(f"动态提取失败: {url} - {e}")
            return []
    
    def _wait_for_content_load(self) -> None:
        """等待内容加载"""
        try:
            # 等待常见的媒体元素
            wait = WebDriverWait(self.driver, 10)
            
            # 尝试等待视频、音频或图片元素
            selectors = [
                "video", "audio", "img", 
                "[data-src]", "[data-original]", 
                ".video", ".audio", ".media"
            ]
            
            for selector in selectors:
                try:
                    wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    logging.debug(f"检测到元素: {selector}")
                    break
                except TimeoutException:
                    continue
                    
        except Exception as e:
            logging.debug(f"等待内容加载异常: {e}")
    
    def _scroll_and_wait(self) -> None:
        """滚动页面并等待内容加载"""
        try:
            # 获取页面高度
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            
            scroll_attempts = 0
            max_scrolls = 10
            
            while scroll_attempts < max_scrolls:
                # 滚动到底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                
                # 等待新内容加载
                time.sleep(2)
                
                # 计算新的页面高度
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                
                if new_height == last_height:
                    # 尝试点击"加载更多"按钮
                    if self._click_load_more():
                        time.sleep(3)
                        new_height = self.driver.execute_script("return document.body.scrollHeight")
                    
                    if new_height == last_height:
                        break
                
                last_height = new_height
                scroll_attempts += 1
            
            # 滚动回顶部
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)
            
            logging.debug(f"页面滚动完成，共滚动 {scroll_attempts} 次")
            
        except Exception as e:
            logging.debug(f"页面滚动异常: {e}")
    
    def _click_load_more(self) -> bool:
        """点击加载更多按钮"""
        try:
            # 常见的"加载更多"按钮选择器
            load_more_selectors = [
                "button:contains('加载更多')",
                "button:contains('Load More')",
                "a:contains('更多')",
                ".load-more",
                ".more-btn",
                "[data-action='load-more']"
            ]
            
            for selector in load_more_selectors:
                try:
                    if selector.startswith("button:contains") or selector.startswith("a:contains"):
                        # 使用XPath查找包含文本的元素
                        text = selector.split("'")[1]
                        xpath = f"//button[contains(text(), '{text}')] | //a[contains(text(), '{text}')]"
                        element = self.driver.find_element(By.XPATH, xpath)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if element.is_displayed() and element.is_enabled():
                        self.driver.execute_script("arguments[0].click();", element)
                        logging.debug(f"点击了加载更多按钮: {selector}")
                        return True
                        
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            logging.debug(f"点击加载更多按钮异常: {e}")
            return False
    
    def _handle_overlays(self) -> None:
        """处理弹窗和覆盖层"""
        try:
            # 常见的关闭按钮选择器
            close_selectors = [
                ".close", ".close-btn", ".modal-close",
                "[data-dismiss='modal']", ".popup-close",
                "button:contains('关闭')", "button:contains('Close')",
                ".overlay .close", ".dialog .close"
            ]
            
            for selector in close_selectors:
                try:
                    if selector.startswith("button:contains"):
                        text = selector.split("'")[1]
                        xpath = f"//button[contains(text(), '{text}')]"
                        elements = self.driver.find_elements(By.XPATH, xpath)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        if element.is_displayed():
                            self.driver.execute_script("arguments[0].click();", element)
                            logging.debug(f"关闭了覆盖层: {selector}")
                            time.sleep(1)
                            break
                            
                except Exception:
                    continue
                    
        except Exception as e:
            logging.debug(f"处理覆盖层异常: {e}")
    
    def _extract_dynamic_media(self) -> List[MediaInfo]:
        """提取动态加载的媒体"""
        media_list = []
        
        try:
            # 查找所有可能的媒体元素
            media_elements = self.driver.find_elements(By.CSS_SELECTOR, 
                "video, audio, img, [data-src], [data-original], [data-lazy]")
            
            current_url = self.driver.current_url
            
            for element in media_elements:
                try:
                    media_info = self._extract_element_info(element, current_url)
                    if media_info:
                        media_list.append(media_info)
                except Exception as e:
                    logging.debug(f"提取元素信息失败: {e}")
            
            # 执行JavaScript获取更多媒体信息
            js_media = self._extract_javascript_media()
            media_list.extend(js_media)
            
        except Exception as e:
            logging.debug(f"提取动态媒体异常: {e}")
        
        return media_list
    
    def _extract_element_info(self, element, base_url: str) -> Optional[MediaInfo]:
        """从元素提取媒体信息"""
        try:
            tag_name = element.tag_name.lower()
            
            # 获取URL
            url = None
            if tag_name in ['video', 'audio']:
                url = element.get_attribute('src')
            elif tag_name == 'img':
                url = (element.get_attribute('src') or 
                       element.get_attribute('data-src') or
                       element.get_attribute('data-original') or
                       element.get_attribute('data-lazy'))
            
            if not url:
                return None
            
            # 转换为绝对URL
            from urllib.parse import urljoin
            absolute_url = urljoin(base_url, url)
            
            # 确定媒体类型
            from ..utils.media_types import MediaTypes, MediaType
            if tag_name == 'video':
                media_type = MediaType.VIDEO
            elif tag_name == 'audio':
                media_type = MediaType.AUDIO
            elif tag_name == 'img':
                media_type = MediaType.IMAGE
            else:
                media_type = MediaTypes.get_media_type_by_extension(absolute_url)
            
            if media_type == MediaType.UNKNOWN:
                return None
            
            # 提取元数据
            title = (element.get_attribute('title') or 
                    element.get_attribute('alt') or '')
            width = element.get_attribute('width') or ''
            height = element.get_attribute('height') or ''
            
            return MediaInfo(
                url=absolute_url,
                media_type=media_type,
                title=title,
                width=width,
                height=height,
                source_page=base_url
            )
            
        except Exception as e:
            logging.debug(f"提取元素信息异常: {e}")
            return None
    
    def _extract_javascript_media(self) -> List[MediaInfo]:
        """通过JavaScript提取媒体信息"""
        media_list = []
        
        try:
            # 执行JavaScript获取所有媒体URL
            js_code = """
            var mediaUrls = [];
            
            // 查找所有可能包含媒体URL的属性
            var elements = document.querySelectorAll('*');
            for (var i = 0; i < elements.length; i++) {
                var el = elements[i];
                var attrs = ['src', 'data-src', 'data-original', 'data-lazy', 'href'];
                
                for (var j = 0; j < attrs.length; j++) {
                    var url = el.getAttribute(attrs[j]);
                    if (url && (url.match(/\\.(mp4|avi|mkv|mov|wmv|flv|webm|mp3|wav|flac|aac|ogg|jpg|jpeg|png|gif|bmp|webp)$/i))) {
                        mediaUrls.push({
                            url: url,
                            tagName: el.tagName.toLowerCase(),
                            title: el.getAttribute('title') || el.getAttribute('alt') || '',
                            width: el.getAttribute('width') || '',
                            height: el.getAttribute('height') || ''
                        });
                    }
                }
            }
            
            return mediaUrls;
            """
            
            js_results = self.driver.execute_script(js_code)
            current_url = self.driver.current_url
            
            for result in js_results:
                try:
                    from urllib.parse import urljoin
                    from ..utils.media_types import MediaTypes
                    
                    absolute_url = urljoin(current_url, result['url'])
                    media_type = MediaTypes.get_media_type_by_extension(absolute_url)
                    
                    if media_type.value != 'unknown':
                        media_info = MediaInfo(
                            url=absolute_url,
                            media_type=media_type,
                            title=result.get('title', ''),
                            width=result.get('width', ''),
                            height=result.get('height', ''),
                            source_page=current_url
                        )
                        media_list.append(media_info)
                        
                except Exception as e:
                    logging.debug(f"处理JavaScript结果异常: {e}")
            
        except Exception as e:
            logging.debug(f"执行JavaScript提取异常: {e}")
        
        return media_list
    
    def _deduplicate_media(self, media_list: List[MediaInfo]) -> List[MediaInfo]:
        """去重媒体列表"""
        seen_urls = set()
        unique_media = []
        
        for media in media_list:
            if media.url not in seen_urls:
                seen_urls.add(media.url)
                unique_media.append(media)
        
        return unique_media
    
    def close(self) -> None:
        """关闭WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                logging.info("WebDriver已关闭")
            except Exception as e:
                logging.warning(f"关闭WebDriver异常: {e}")
            finally:
                self.driver = None
