"""
反爬虫处理模块
==============

处理反爬虫机制，包括User-Agent轮换、代理支持、请求延迟等
"""

import random
import time
import logging
from typing import Dict, List, Optional, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class AntiCrawler:
    """反爬虫处理器"""
    
    def __init__(self, config=None):
        """初始化反爬虫处理器"""
        self.config = config
        self.session = None
        self.current_user_agent_index = 0
        self.request_count = 0
        self.last_request_time = 0
        
        # 用户代理列表
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Android 14; Mobile; rv:121.0) Gecko/121.0 Firefox/121.0',
            'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36'
        ]
        
        # 常见请求头
        self.common_headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        
        self._setup_session()
    
    def _setup_session(self) -> None:
        """设置请求会话"""
        self.session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=self.config.network.max_retries if self.config else 3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置代理
        if self.config and self.config.network.use_proxy and self.config.network.proxy_url:
            self.session.proxies = {
                'http': self.config.network.proxy_url,
                'https': self.config.network.proxy_url
            }
        
        # 设置SSL验证
        if self.config:
            self.session.verify = self.config.network.verify_ssl
    
    def get_headers(self, url: str = None) -> Dict[str, str]:
        """获取请求头"""
        headers = self.common_headers.copy()
        
        # 设置User-Agent
        if self.config and self.config.crawler.user_agent_rotation:
            headers['User-Agent'] = self._get_random_user_agent()
        else:
            headers['User-Agent'] = self.user_agents[0]
        
        # 根据URL设置特定的Referer
        if url:
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            headers['Referer'] = f"{parsed_url.scheme}://{parsed_url.netloc}/"
        
        return headers
    
    def _get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        return random.choice(self.user_agents)
    
    def _apply_request_delay(self) -> None:
        """应用请求延迟"""
        if not self.config:
            return
        
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.config.crawler.request_delay:
            sleep_time = self.config.crawler.request_delay - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def make_request(self, url: str, method: str = 'GET', **kwargs) -> requests.Response:
        """发起请求"""
        self._apply_request_delay()
        
        # 设置默认参数
        kwargs.setdefault('headers', self.get_headers(url))
        kwargs.setdefault('timeout', self.config.network.timeout if self.config else 30)
        
        # 记录请求
        self.request_count += 1
        logging.debug(f"发起请求 #{self.request_count}: {method} {url}")
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        
        except requests.exceptions.RequestException as e:
            logging.error(f"请求失败: {url} - {e}")
            raise
    
    def get_selenium_options(self, browser: str = 'chrome') -> Any:
        """获取Selenium浏览器选项"""
        if browser.lower() == 'chrome':
            from selenium.webdriver.chrome.options import Options
            options = Options()
            
            if self.config and self.config.crawler.headless:
                options.add_argument('--headless')
            
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 设置窗口大小
            if self.config:
                width, height = self.config.crawler.window_size
                options.add_argument(f'--window-size={width},{height}')
            
            # 设置User-Agent
            user_agent = self._get_random_user_agent()
            options.add_argument(f'--user-agent={user_agent}')
            
            # 设置代理
            if self.config and self.config.network.use_proxy and self.config.network.proxy_url:
                options.add_argument(f'--proxy-server={self.config.network.proxy_url}')
            
            return options
        
        elif browser.lower() == 'firefox':
            from selenium.webdriver.firefox.options import Options
            options = Options()
            
            if self.config and self.config.crawler.headless:
                options.add_argument('--headless')
            
            # 设置User-Agent
            user_agent = self._get_random_user_agent()
            options.set_preference("general.useragent.override", user_agent)
            
            # 设置代理
            if self.config and self.config.network.use_proxy and self.config.network.proxy_url:
                from urllib.parse import urlparse
                proxy_url = urlparse(self.config.network.proxy_url)
                options.set_preference("network.proxy.type", 1)
                options.set_preference("network.proxy.http", proxy_url.hostname)
                options.set_preference("network.proxy.http_port", proxy_url.port)
                options.set_preference("network.proxy.ssl", proxy_url.hostname)
                options.set_preference("network.proxy.ssl_port", proxy_url.port)
            
            return options
        
        else:
            raise ValueError(f"不支持的浏览器类型: {browser}")
    
    def setup_selenium_driver(self, driver) -> None:
        """设置Selenium驱动器"""
        # 执行反检测脚本
        driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
        
        # 设置超时
        if self.config:
            driver.implicitly_wait(self.config.crawler.implicit_wait)
            driver.set_page_load_timeout(self.config.crawler.page_load_timeout)
    
    def is_blocked(self, response: requests.Response) -> bool:
        """检查是否被阻止"""
        # 检查状态码
        if response.status_code in [403, 429, 503]:
            return True
        
        # 检查响应内容中的阻止关键词
        content = response.text.lower()
        block_keywords = [
            'access denied', 'blocked', 'forbidden', 'captcha',
            '访问被拒绝', '被阻止', '验证码', '人机验证'
        ]
        
        for keyword in block_keywords:
            if keyword in content:
                return True
        
        return False
    
    def handle_captcha(self, driver=None) -> bool:
        """处理验证码（基础实现）"""
        logging.warning("检测到验证码，需要人工处理")
        
        if driver:
            # 等待用户手动处理验证码
            input("请手动完成验证码验证，然后按回车继续...")
            return True
        
        return False
    
    def get_session_info(self) -> Dict[str, Any]:
        """获取会话信息"""
        return {
            'request_count': self.request_count,
            'current_user_agent': self.user_agents[self.current_user_agent_index],
            'proxy_enabled': self.config.network.use_proxy if self.config else False,
            'last_request_time': self.last_request_time
        }
