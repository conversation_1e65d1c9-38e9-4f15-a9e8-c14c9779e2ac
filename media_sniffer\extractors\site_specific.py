"""
特定网站处理器
==============

针对YouTube、Bilibili等特定网站的专门处理
"""

import re
import json
import logging
from typing import List, Dict, Optional, Any
from urllib.parse import urlparse, parse_qs
import yt_dlp
from ..core.parser import MediaInfo
from ..utils.media_types import MediaType

class SiteSpecificExtractor:
    """特定网站处理器"""
    
    def __init__(self, config=None):
        """初始化特定网站处理器"""
        self.config = config
        
        # 支持的网站处理器映射
        self.site_handlers = {
            'youtube.com': self._handle_youtube,
            'youtu.be': self._handle_youtube,
            'bilibili.com': self._handle_bilibili,
            'b23.tv': self._handle_bilibili,
            'vimeo.com': self._handle_vimeo,
            'dailymotion.com': self._handle_dailymotion,
            'twitch.tv': self._handle_twitch,
        }
        
        # yt-dlp配置
        self.ytdl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': False,
            'writeinfojson': False,
            'writethumbnail': False,
            'writesubtitles': False,
        }
    
    def can_handle(self, url: str) -> bool:
        """检查是否可以处理该URL"""
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()
            
            # 移除www前缀
            if domain.startswith('www.'):
                domain = domain[4:]
            
            return domain in self.site_handlers
            
        except Exception:
            return False
    
    def extract_from_url(self, url: str) -> List[MediaInfo]:
        """从特定网站URL提取媒体"""
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()
            
            # 移除www前缀
            if domain.startswith('www.'):
                domain = domain[4:]
            
            if domain in self.site_handlers:
                handler = self.site_handlers[domain]
                return handler(url)
            else:
                logging.warning(f"不支持的网站: {domain}")
                return []
                
        except Exception as e:
            logging.error(f"特定网站提取失败: {url} - {e}")
            return []
    
    def _handle_youtube(self, url: str) -> List[MediaInfo]:
        """处理YouTube视频"""
        try:
            logging.info(f"处理YouTube视频: {url}")
            
            with yt_dlp.YoutubeDL(self.ytdl_opts) as ydl:
                # 提取视频信息
                info = ydl.extract_info(url, download=False)
                
                media_list = []
                
                if 'entries' in info:
                    # 播放列表
                    for entry in info['entries']:
                        if entry:
                            media_info = self._create_youtube_media_info(entry, url)
                            if media_info:
                                media_list.append(media_info)
                else:
                    # 单个视频
                    media_info = self._create_youtube_media_info(info, url)
                    if media_info:
                        media_list.append(media_info)
                
                logging.info(f"YouTube提取完成，找到 {len(media_list)} 个媒体文件")
                return media_list
                
        except Exception as e:
            logging.error(f"YouTube处理失败: {url} - {e}")
            return []
    
    def _create_youtube_media_info(self, info: Dict, source_url: str) -> Optional[MediaInfo]:
        """创建YouTube媒体信息"""
        try:
            # 获取最佳质量的视频URL
            formats = info.get('formats', [])
            best_video = None
            best_audio = None
            
            for fmt in formats:
                if fmt.get('vcodec') != 'none' and fmt.get('acodec') != 'none':
                    # 包含视频和音频
                    if not best_video or fmt.get('height', 0) > best_video.get('height', 0):
                        best_video = fmt
                elif fmt.get('acodec') != 'none' and fmt.get('vcodec') == 'none':
                    # 仅音频
                    if not best_audio or fmt.get('abr', 0) > best_audio.get('abr', 0):
                        best_audio = fmt
            
            # 优先选择包含视频的格式
            selected_format = best_video or best_audio
            
            if not selected_format:
                return None
            
            # 创建媒体信息
            media_info = MediaInfo(
                url=selected_format.get('url', ''),
                media_type=MediaType.VIDEO if best_video else MediaType.AUDIO,
                title=info.get('title', ''),
                description=info.get('description', ''),
                thumbnail=info.get('thumbnail', ''),
                duration=self._format_duration(info.get('duration')),
                quality=f"{selected_format.get('height', '')}p" if selected_format.get('height') else '',
                source_page=source_url,
                metadata={
                    'uploader': info.get('uploader', ''),
                    'upload_date': info.get('upload_date', ''),
                    'view_count': info.get('view_count', 0),
                    'like_count': info.get('like_count', 0),
                    'channel': info.get('channel', ''),
                    'channel_url': info.get('channel_url', ''),
                }
            )
            
            return media_info
            
        except Exception as e:
            logging.debug(f"创建YouTube媒体信息失败: {e}")
            return None
    
    def _handle_bilibili(self, url: str) -> List[MediaInfo]:
        """处理Bilibili视频"""
        try:
            logging.info(f"处理Bilibili视频: {url}")
            
            with yt_dlp.YoutubeDL(self.ytdl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                media_list = []
                
                if 'entries' in info:
                    # 多P视频
                    for entry in info['entries']:
                        if entry:
                            media_info = self._create_bilibili_media_info(entry, url)
                            if media_info:
                                media_list.append(media_info)
                else:
                    # 单个视频
                    media_info = self._create_bilibili_media_info(info, url)
                    if media_info:
                        media_list.append(media_info)
                
                logging.info(f"Bilibili提取完成，找到 {len(media_list)} 个媒体文件")
                return media_list
                
        except Exception as e:
            logging.error(f"Bilibili处理失败: {url} - {e}")
            return []
    
    def _create_bilibili_media_info(self, info: Dict, source_url: str) -> Optional[MediaInfo]:
        """创建Bilibili媒体信息"""
        try:
            # 获取最佳质量的视频
            formats = info.get('formats', [])
            best_format = None
            
            for fmt in formats:
                if fmt.get('vcodec') != 'none':
                    if not best_format or fmt.get('height', 0) > best_format.get('height', 0):
                        best_format = fmt
            
            if not best_format:
                return None
            
            media_info = MediaInfo(
                url=best_format.get('url', ''),
                media_type=MediaType.VIDEO,
                title=info.get('title', ''),
                description=info.get('description', ''),
                thumbnail=info.get('thumbnail', ''),
                duration=self._format_duration(info.get('duration')),
                quality=f"{best_format.get('height', '')}p" if best_format.get('height') else '',
                source_page=source_url,
                metadata={
                    'uploader': info.get('uploader', ''),
                    'upload_date': info.get('upload_date', ''),
                    'view_count': info.get('view_count', 0),
                    'like_count': info.get('like_count', 0),
                    'bvid': info.get('id', ''),
                }
            )
            
            return media_info
            
        except Exception as e:
            logging.debug(f"创建Bilibili媒体信息失败: {e}")
            return None
    
    def _handle_vimeo(self, url: str) -> List[MediaInfo]:
        """处理Vimeo视频"""
        try:
            logging.info(f"处理Vimeo视频: {url}")
            
            with yt_dlp.YoutubeDL(self.ytdl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                media_info = self._create_generic_media_info(info, url, MediaType.VIDEO)
                return [media_info] if media_info else []
                
        except Exception as e:
            logging.error(f"Vimeo处理失败: {url} - {e}")
            return []
    
    def _handle_dailymotion(self, url: str) -> List[MediaInfo]:
        """处理Dailymotion视频"""
        try:
            logging.info(f"处理Dailymotion视频: {url}")
            
            with yt_dlp.YoutubeDL(self.ytdl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                media_info = self._create_generic_media_info(info, url, MediaType.VIDEO)
                return [media_info] if media_info else []
                
        except Exception as e:
            logging.error(f"Dailymotion处理失败: {url} - {e}")
            return []
    
    def _handle_twitch(self, url: str) -> List[MediaInfo]:
        """处理Twitch视频"""
        try:
            logging.info(f"处理Twitch视频: {url}")
            
            with yt_dlp.YoutubeDL(self.ytdl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                media_info = self._create_generic_media_info(info, url, MediaType.VIDEO)
                return [media_info] if media_info else []
                
        except Exception as e:
            logging.error(f"Twitch处理失败: {url} - {e}")
            return []
    
    def _create_generic_media_info(self, info: Dict, source_url: str, 
                                 media_type: MediaType) -> Optional[MediaInfo]:
        """创建通用媒体信息"""
        try:
            formats = info.get('formats', [])
            if not formats:
                return None
            
            # 选择最佳格式
            best_format = formats[-1]  # 通常最后一个是最佳质量
            
            media_info = MediaInfo(
                url=best_format.get('url', ''),
                media_type=media_type,
                title=info.get('title', ''),
                description=info.get('description', ''),
                thumbnail=info.get('thumbnail', ''),
                duration=self._format_duration(info.get('duration')),
                quality=f"{best_format.get('height', '')}p" if best_format.get('height') else '',
                source_page=source_url,
                metadata={
                    'uploader': info.get('uploader', ''),
                    'upload_date': info.get('upload_date', ''),
                    'view_count': info.get('view_count', 0),
                }
            )
            
            return media_info
            
        except Exception as e:
            logging.debug(f"创建通用媒体信息失败: {e}")
            return None
    
    def _format_duration(self, duration: Optional[int]) -> str:
        """格式化时长"""
        if not duration:
            return ""
        
        try:
            hours = duration // 3600
            minutes = (duration % 3600) // 60
            seconds = duration % 60
            
            if hours > 0:
                return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            else:
                return f"{minutes:02d}:{seconds:02d}"
                
        except Exception:
            return str(duration)
    
    def get_supported_sites(self) -> List[str]:
        """获取支持的网站列表"""
        return list(self.site_handlers.keys())
    
    def extract_with_quality_options(self, url: str) -> Dict[str, List[MediaInfo]]:
        """提取多种质量选项的媒体"""
        try:
            with yt_dlp.YoutubeDL(self.ytdl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                quality_options = {}
                formats = info.get('formats', [])
                
                for fmt in formats:
                    if fmt.get('vcodec') != 'none':
                        height = fmt.get('height', 0)
                        quality = f"{height}p" if height else "unknown"
                        
                        if quality not in quality_options:
                            quality_options[quality] = []
                        
                        media_info = MediaInfo(
                            url=fmt.get('url', ''),
                            media_type=MediaType.VIDEO,
                            title=info.get('title', ''),
                            quality=quality,
                            source_page=url,
                            metadata={'format_id': fmt.get('format_id', '')}
                        )
                        
                        quality_options[quality].append(media_info)
                
                return quality_options
                
        except Exception as e:
            logging.error(f"提取质量选项失败: {url} - {e}")
            return {}
