# 网页多媒体嗅探程序

一个功能全面的网页多媒体内容检测和下载工具，支持检测网页中的视频、音频、图片等多媒体文件。

## 🌟 主要功能

- **多媒体检测**: 自动检测网页中的视频、音频、图片文件
- **静态内容解析**: 解析HTML中的直接媒体链接
- **动态内容支持**: 使用Selenium处理JavaScript动态加载的内容
- **特定网站优化**: 针对YouTube、Bilibili等网站的专门处理
- **批量下载**: 支持多线程下载和断点续传
- **多种输出格式**: JSON、CSV、Excel格式导出
- **反爬虫机制**: User-Agent轮换、代理支持、请求延迟
- **配置灵活**: 丰富的配置选项和命令行参数

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

### 浏览器驱动安装

程序会自动下载Chrome和Firefox驱动，但请确保已安装对应浏览器：

- **Chrome**: 下载并安装 [Google Chrome](https://www.google.com/chrome/)
- **Firefox**: 下载并安装 [Mozilla Firefox](https://www.mozilla.org/firefox/)

## 🚀 快速开始

### 基础用法

```bash
# 检测网页中的所有媒体文件
python main.py https://example.com

# 启用动态内容检测
python main.py https://example.com --dynamic

# 只检测视频和音频
python main.py https://example.com --types video audio

# 启用下载功能
python main.py https://example.com --download
```

### 高级用法

```bash
# 导出为Excel格式
python main.py https://example.com --output excel --file results

# 使用代理
python main.py https://example.com --proxy http://proxy.example.com:8080

# 批量处理URL列表
python main.py --batch urls.txt --output json

# 自定义配置文件
python main.py https://example.com --config custom_config.json
```

## 📋 命令行参数

### 必需参数
- `url`: 要检测的网页URL

### 检测选项
- `--types {video,audio,image}`: 指定要检测的媒体类型
- `--dynamic`: 启用动态内容检测（使用浏览器）
- `--no-static`: 禁用静态内容检测
- `--no-site-specific`: 禁用特定网站处理

### 输出选项
- `--output {json,csv,excel}`: 输出格式（默认: json）
- `--file`: 输出文件名（不含扩展名）
- `--no-display`: 不在控制台显示结果
- `--max-display`: 控制台最多显示的媒体数量

### 下载选项
- `--download`: 启用媒体文件下载
- `--download-path`: 下载目录路径
- `--max-size`: 最大文件大小限制（字节）

### 配置选项
- `--config`: 配置文件路径
- `--log-level {DEBUG,INFO,WARNING,ERROR}`: 日志级别
- `--log-file`: 日志文件路径

### 网络选项
- `--timeout`: 请求超时时间（秒）
- `--user-agent`: 自定义User-Agent
- `--proxy`: 代理服务器URL

## ⚙️ 配置文件

程序使用JSON格式的配置文件，默认为`config.json`：

```json
{
  "network": {
    "timeout": 30,
    "max_retries": 3,
    "max_concurrent_downloads": 5,
    "use_proxy": false,
    "proxy_url": null
  },
  "crawler": {
    "enable_javascript": true,
    "page_load_timeout": 30,
    "headless": true,
    "user_agent_rotation": true,
    "request_delay": 1.0
  },
  "download": {
    "enable_download": false,
    "download_path": "./downloads",
    "create_subdirs": true,
    "max_file_size": 1073741824
  },
  "output": {
    "output_format": "json",
    "include_thumbnails": true,
    "include_metadata": true
  }
}
```

## 🎯 支持的网站

### 特殊优化支持
- YouTube (youtube.com, youtu.be)
- Bilibili (bilibili.com, b23.tv)
- Vimeo (vimeo.com)
- Dailymotion (dailymotion.com)
- Twitch (twitch.tv)

### 通用支持
- 所有包含HTML媒体标签的网站
- 支持JavaScript动态加载的网站
- 支持CSS背景图片提取

## 📊 输出格式

### JSON格式
```json
{
  "total_found": 15,
  "by_type": {
    "video": 5,
    "audio": 3,
    "image": 7
  },
  "media_list": [
    {
      "url": "https://example.com/video.mp4",
      "type": "video",
      "title": "示例视频",
      "description": "视频描述",
      "file_size": "10.5 MB",
      "duration": "02:30",
      "quality": "720p"
    }
  ]
}
```

### Excel格式
包含以下列：
- URL: 媒体文件链接
- Type: 媒体类型（video/audio/image）
- Title: 标题
- Description: 描述
- File Size: 文件大小
- Duration: 时长（视频/音频）
- Quality: 质量
- Source Page: 来源页面

## 🔧 编程接口

### 基础使用

```python
from media_sniffer import MediaDetector
from media_sniffer.utils.config import Config

# 创建配置
config = Config()
config.crawler.enable_javascript = True
config.download.enable_download = True

# 创建检测器
detector = MediaDetector(config)

# 检测媒体
result = detector.detect_from_url("https://example.com")

# 查看结果
print(f"找到 {result.total_found} 个媒体文件")
for media in result.media_list:
    print(f"{media.media_type.value}: {media.url}")

# 导出结果
detector.export_results(result, "excel", "results")
```

### 高级功能

```python
# 批量检测
urls = ["https://site1.com", "https://site2.com"]
results = detector.detect_batch(urls)

# 过滤媒体
filtered_media = detector.filter_media(
    result.media_list,
    media_types=["video"],
    keywords=["教程", "演示"]
)

# 获取统计信息
stats = detector.get_statistics(result)
print(f"处理时间: {stats['summary']['processing_time']:.2f}秒")
```

## 🐛 常见问题

### 1. 浏览器驱动问题
```bash
# 如果自动下载失败，手动安装驱动
pip install webdriver-manager --upgrade
```

### 2. 权限问题
```bash
# Linux/Mac 需要执行权限
chmod +x main.py
```

### 3. 网络连接问题
```bash
# 使用代理
python main.py https://example.com --proxy http://proxy:8080
```

### 4. 内存占用过高
- 减少并发数：修改配置中的`max_concurrent_downloads`
- 禁用动态检测：不使用`--dynamic`参数
- 限制文件大小：使用`--max-size`参数

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持静态和动态内容检测
- 支持多种输出格式
- 支持批量下载功能
- 支持特定网站优化

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发者

---

**注意**: 请遵守网站的robots.txt和使用条款，合理使用本工具。
