#!/usr/bin/env python3
"""
Bilibili音频分析脚本
===================

分析Bilibili视频页面，查找音频流
"""

import sys
import re
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from media_sniffer import MediaDetector
from media_sniffer.utils.config import Config
from media_sniffer.extractors.site_specific import SiteSpecificExtractor

def analyze_bilibili_url(url):
    """分析Bilibili URL"""
    print(f"🔍 分析Bilibili视频: {url}")
    print("=" * 60)
    
    # 创建配置
    config = Config()
    config.crawler.enable_javascript = False  # 先用静态分析
    
    # 创建检测器
    detector = MediaDetector(config)
    
    print("📋 1. 使用特定网站处理器分析...")
    try:
        site_extractor = SiteSpecificExtractor(config)
        if site_extractor.can_handle(url):
            site_media = site_extractor.extract_from_url(url)
            print(f"   找到 {len(site_media)} 个媒体文件:")
            
            for i, media in enumerate(site_media, 1):
                print(f"   {i}. [{media.media_type.value.upper()}] {media.title}")
                print(f"      URL: {media.url[:100]}...")
                print(f"      质量: {media.quality}")
                print(f"      时长: {media.duration}")
                if media.metadata:
                    format_id = media.metadata.get('format_id', '')
                    if format_id:
                        print(f"      格式ID: {format_id}")
                print()
        else:
            print("   ❌ 不支持此URL")
    except Exception as e:
        print(f"   ❌ 特定网站处理失败: {e}")
    
    print("📋 2. 使用完整检测器分析...")
    try:
        result = detector.detect_from_url(url, enable_dynamic=False)
        
        print(f"   总计: {result.total_found} 个媒体文件")
        print(f"   视频: {result.by_type['video']} 个")
        print(f"   音频: {result.by_type['audio']} 个")
        print(f"   图片: {result.by_type['image']} 个")
        print()
        
        # 显示前几个媒体文件
        print("   前5个媒体文件:")
        for i, media in enumerate(result.media_list[:5], 1):
            print(f"   {i}. [{media.media_type.value.upper()}] {media.url[:80]}...")
            if media.title:
                print(f"      标题: {media.title}")
        
        if len(result.media_list) > 5:
            print(f"   ... 还有 {len(result.media_list) - 5} 个文件")
        print()
        
    except Exception as e:
        print(f"   ❌ 完整检测失败: {e}")
    
    detector.close()

def analyze_media_patterns():
    """分析媒体URL模式"""
    print("🎯 分析常见的媒体URL模式")
    print("=" * 60)
    
    # 从之前的结果文件中提取URL模式
    patterns = {
        'bilibili_video_m4s': r'\.mcdn\.bilivideo\.cn.*\.m4s',
        'bilibili_video_mp4': r'upos-.*\.bilivideo\.com.*\.mp4',
        'bilibili_audio_m4s': r'\.mcdn\.bilivideo\.cn.*audio.*\.m4s',
        'bilibili_audio_mp3': r'\.bilivideo\.com.*\.mp3',
        'bilibili_audio_aac': r'\.bilivideo\.com.*\.aac',
    }
    
    # 示例URL（从实际结果中提取）
    test_urls = [
        "https://xy39x174x253x210xy.mcdn.bilivideo.cn:8082/v1/resource/25827875444-1-100050.m4s",
        "https://upos-sz-estgcos.bilivideo.com/upgcxcode/44/54/25827875444/25827875444-1-16.mp4",
        "https://example.bilivideo.com/audio/12345-audio.m4s",
        "https://example.bilivideo.com/sound/12345.aac"
    ]
    
    for url in test_urls:
        print(f"📁 {url}")
        
        # 检查匹配的模式
        matched_patterns = []
        for pattern_name, pattern in patterns.items():
            if re.search(pattern, url):
                matched_patterns.append(pattern_name)
        
        if matched_patterns:
            print(f"   匹配模式: {', '.join(matched_patterns)}")
        else:
            print("   无匹配模式")
        
        # 分析URL结构
        if 'audio' in url.lower():
            print("   🎵 可能是音频文件（URL包含'audio'）")
        elif 'video' in url.lower():
            print("   🎬 可能是视频文件（URL包含'video'）")
        elif '.m4s' in url:
            print("   📹 DASH分段文件（可能是视频流）")
        elif '.mp4' in url:
            print("   🎬 MP4视频文件")
        
        print()

def suggest_improvements():
    """建议改进方案"""
    print("💡 改进建议")
    print("=" * 60)
    
    suggestions = [
        "1. 增强yt-dlp提取：确保获取所有音频和视频流",
        "2. URL模式识别：根据URL路径判断是否为音频流",
        "3. 元数据分析：检查format_id中的音频标识",
        "4. JavaScript分析：深入分析页面中的播放器配置",
        "5. API调用：尝试调用Bilibili的API获取完整媒体信息"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")
    print()
    
    print("🔧 具体实现方案:")
    print("   - 修改site_specific.py中的Bilibili处理器")
    print("   - 增加音频流的专门检测逻辑")
    print("   - 改进yt-dlp的配置，确保提取音频格式")
    print("   - 添加URL模式匹配来识别音频流")
    print()

def main():
    """主函数"""
    print("🎵 Bilibili音频检测分析")
    print("=" * 60)
    print()
    
    # 测试URL（使用您之前提供的）
    test_url = "https://www.bilibili.com/video/BV1WigzztEyx/?spm_id_from=333.1007.tianma.1-1-1.click"
    
    try:
        analyze_bilibili_url(test_url)
        analyze_media_patterns()
        suggest_improvements()
        
        print("🎯 结论:")
        print("   - Bilibili使用DASH技术，音频和视频是分离的")
        print("   - .m4s文件确实是视频流（通常无音频）")
        print("   - 音频流可能使用不同的URL模式或需要特殊提取")
        print("   - 需要改进yt-dlp配置来获取音频流")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
