#!/usr/bin/env python3
"""
网页多媒体嗅探程序主入口
========================

一个功能全面的网页多媒体内容检测和下载工具

使用方法:
    python main.py <URL> [选项]

示例:
    python main.py https://example.com
    python main.py https://example.com --download --output json
    python main.py https://example.com --types video audio --dynamic
"""

import sys
import argparse
import json
import logging
from pathlib import Path
from typing import List, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from media_sniffer import MediaDetector
from media_sniffer.utils.config import Config
from media_sniffer.core.parser import MediaInfo

def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """设置日志"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 文件输出
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    网页多媒体嗅探程序                          ║
║                  Web Media Sniffer v1.0.0                   ║
║                                                              ║
║  功能: 检测网页中的视频、音频、图片等多媒体内容                  ║
║  支持: 静态内容、动态内容、特定网站优化                         ║
║  作者: AI Assistant                                          ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_results_summary(result):
    """打印结果摘要"""
    print(f"\n{'='*60}")
    print(f"检测完成！")
    print(f"{'='*60}")
    print(f"总计找到: {result.total_found} 个媒体文件")
    print(f"处理时间: {result.processing_time:.2f} 秒")
    print(f"")
    print(f"按类型分布:")
    for media_type, count in result.by_type.items():
        if count > 0:
            print(f"  {media_type.capitalize()}: {count}")
    
    print(f"")
    print(f"按来源分布:")
    for source, count in result.by_source.items():
        if count > 0:
            print(f"  {source.replace('_', ' ').title()}: {count}")
    
    if result.errors:
        print(f"")
        print(f"错误信息:")
        for error in result.errors:
            print(f"  ⚠️  {error}")

def print_media_list(media_list: List[MediaInfo], max_display: int = 20):
    """打印媒体列表"""
    if not media_list:
        print("未找到媒体文件。")
        return
    
    print(f"\n{'='*60}")
    print(f"媒体文件列表 (显示前 {min(len(media_list), max_display)} 个)")
    print(f"{'='*60}")
    
    for i, media in enumerate(media_list[:max_display], 1):
        print(f"\n{i}. [{media.media_type.value.upper()}] {media.title or '无标题'}")
        print(f"   URL: {media.url}")
        if media.description:
            print(f"   描述: {media.description[:100]}{'...' if len(media.description) > 100 else ''}")
        if media.file_size:
            print(f"   大小: {media.file_size}")
        if media.duration:
            print(f"   时长: {media.duration}")
        if media.quality:
            print(f"   质量: {media.quality}")
    
    if len(media_list) > max_display:
        print(f"\n... 还有 {len(media_list) - max_display} 个文件未显示")

def save_results(result, output_format: str, output_file: str = None):
    """保存结果"""
    try:
        detector = MediaDetector()
        
        if not output_file:
            import time
            timestamp = int(time.time())
            output_file = f"media_results_{timestamp}"
        
        filepath = detector.export_results(result, output_format, output_file)
        print(f"\n✅ 结果已保存到: {filepath}")
        return filepath
        
    except Exception as e:
        print(f"\n❌ 保存结果失败: {e}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="网页多媒体嗅探程序",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py https://example.com
  python main.py https://example.com --download
  python main.py https://example.com --types video audio
  python main.py https://example.com --output excel --file results
  python main.py https://example.com --dynamic --no-static
  python main.py https://example.com --config custom_config.json
        """
    )
    
    # 必需参数
    parser.add_argument('url', help='要检测的网页URL')
    
    # 检测选项
    parser.add_argument('--types', nargs='+', choices=['video', 'audio', 'image'],
                       help='指定要检测的媒体类型 (默认: 所有类型)')
    parser.add_argument('--dynamic', action='store_true',
                       help='启用动态内容检测 (使用浏览器)')
    parser.add_argument('--no-dynamic', action='store_true',
                       help='禁用动态内容检测')
    parser.add_argument('--no-static', action='store_true',
                       help='禁用静态内容检测')
    parser.add_argument('--no-site-specific', action='store_true',
                       help='禁用特定网站处理')
    
    # 输出选项
    parser.add_argument('--output', choices=['json', 'csv', 'excel'], default='json',
                       help='输出格式 (默认: json)')
    parser.add_argument('--file', help='输出文件名 (不含扩展名)')
    parser.add_argument('--no-display', action='store_true',
                       help='不在控制台显示结果')
    parser.add_argument('--max-display', type=int, default=20,
                       help='控制台最多显示的媒体数量 (默认: 20)')
    
    # 下载选项
    parser.add_argument('--download', action='store_true',
                       help='启用媒体文件下载')
    parser.add_argument('--download-path', help='下载目录路径')
    parser.add_argument('--max-size', type=int,
                       help='最大文件大小限制 (字节)')
    
    # 配置选项
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别 (默认: INFO)')
    parser.add_argument('--log-file', help='日志文件路径')
    
    # 其他选项
    parser.add_argument('--timeout', type=int, help='请求超时时间 (秒)')
    parser.add_argument('--user-agent', help='自定义User-Agent')
    parser.add_argument('--proxy', help='代理服务器URL')
    parser.add_argument('--batch', help='批量处理URL列表文件')
    
    args = parser.parse_args()
    
    # 打印横幅
    if not args.no_display:
        print_banner()
    
    # 设置日志
    setup_logging(args.log_level, args.log_file)
    
    try:
        # 加载配置
        config = Config(args.config)
        
        # 应用命令行参数覆盖配置
        if args.timeout:
            config.network.timeout = args.timeout
        if args.user_agent:
            config.crawler.user_agent_rotation = False
        if args.proxy:
            config.network.use_proxy = True
            config.network.proxy_url = args.proxy
        if args.download:
            config.download.enable_download = True
        if args.download_path:
            config.download.download_path = args.download_path
        if args.max_size:
            config.download.max_file_size = args.max_size
        if args.dynamic:
            config.crawler.enable_javascript = True
        elif args.no_dynamic:
            config.crawler.enable_javascript = False
        
        # 创建检测器
        detector = MediaDetector(config)
        
        # 执行检测
        if args.batch:
            # 批量处理
            with open(args.batch, 'r', encoding='utf-8') as f:
                urls = [line.strip() for line in f if line.strip()]
            
            print(f"开始批量处理 {len(urls)} 个URL...")
            results = detector.detect_batch(urls)
            
            # 合并结果
            from media_sniffer.core.detector import DetectionResult
            combined_result = DetectionResult()
            for result in results:
                combined_result.add_media(result.media_list, 'batch')
                combined_result.errors.extend(result.errors)
            
            result = combined_result
        else:
            # 单个URL处理
            result = detector.detect_from_url(
                args.url,
                enable_dynamic=args.dynamic,
                enable_download=args.download
            )
        
        # 过滤结果
        if args.types:
            result.media_list = detector.filter_media(
                result.media_list,
                media_types=args.types
            )
            result.total_found = len(result.media_list)
        
        # 显示结果
        if not args.no_display:
            print_results_summary(result)
            print_media_list(result.media_list, args.max_display)
        
        # 保存结果
        if args.output or args.file:
            save_results(result, args.output, args.file)
        
        # 清理资源
        detector.close()
        
        print(f"\n🎉 程序执行完成！")
        
    except KeyboardInterrupt:
        print(f"\n\n⚠️  程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        logging.exception("程序异常")
        sys.exit(1)

if __name__ == "__main__":
    main()
