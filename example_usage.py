#!/usr/bin/env python3
"""
使用示例脚本
============

演示如何在Python代码中使用媒体嗅探程序
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from media_sniffer import MediaDetector
from media_sniffer.utils.config import Config

def example_1_basic_detection():
    """示例1：基础检测"""
    print("📋 示例1：基础媒体检测")
    print("=" * 40)
    
    # 创建配置
    config = Config()
    config.crawler.enable_javascript = False  # 禁用动态检测以提高速度
    
    # 创建检测器
    detector = MediaDetector(config)
    
    # 测试HTML内容
    html_content = """
    <!DOCTYPE html>
    <html>
    <head><title>测试页面</title></head>
    <body>
        <h1>媒体内容测试</h1>
        <video src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" controls></video>
        <audio src="https://sample-videos.com/zip/10/mp3/SampleAudio_0.4mb.mp3" controls></audio>
        <img src="https://picsum.photos/800/600" alt="随机图片">
        <a href="https://sample-videos.com/zip/10/mp4/SampleVideo_720x480_1mb.mp4">下载视频</a>
    </body>
    </html>
    """
    
    # 检测媒体
    result = detector.detect_from_html(html_content, "https://example.com")
    
    print(f"✅ 找到 {result.total_found} 个媒体文件")
    print(f"⏱️  处理时间: {result.processing_time:.3f} 秒")
    print()
    
    for i, media in enumerate(result.media_list, 1):
        print(f"{i}. [{media.media_type.value.upper()}] {media.url}")
        if media.title:
            print(f"   标题: {media.title}")
    
    detector.close()
    print()

def example_2_url_detection():
    """示例2：URL检测"""
    print("🌐 示例2：网页URL检测")
    print("=" * 40)
    
    # 创建配置
    config = Config()
    config.crawler.enable_javascript = False
    config.network.timeout = 30
    
    # 创建检测器
    detector = MediaDetector(config)
    
    # 测试URL
    test_url = "https://httpbin.org/html"
    
    try:
        result = detector.detect_from_url(test_url)
        
        print(f"🔍 检测URL: {test_url}")
        print(f"✅ 找到 {result.total_found} 个媒体文件")
        print(f"⏱️  处理时间: {result.processing_time:.3f} 秒")
        
        if result.errors:
            print("⚠️  错误信息:")
            for error in result.errors:
                print(f"   {error}")
        
        # 按类型统计
        print("\n📊 按类型分布:")
        for media_type, count in result.by_type.items():
            if count > 0:
                print(f"   {media_type.capitalize()}: {count}")
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
    
    detector.close()
    print()

def example_3_media_filtering():
    """示例3：媒体过滤"""
    print("🔍 示例3：媒体过滤")
    print("=" * 40)
    
    # 创建检测器
    detector = MediaDetector()
    
    # 测试HTML内容（包含多种媒体）
    html_content = """
    <html>
    <body>
        <video src="https://example.com/video1.mp4" title="教程视频"></video>
        <video src="https://example.com/video2.avi" title="演示视频"></video>
        <audio src="https://example.com/music1.mp3" title="背景音乐"></audio>
        <audio src="https://example.com/sound.wav" title="音效"></audio>
        <img src="https://example.com/photo1.jpg" alt="产品图片" title="产品展示">
        <img src="https://example.com/logo.png" alt="公司logo" title="品牌标识">
        <img src="https://example.com/banner.gif" alt="广告横幅">
    </body>
    </html>
    """
    
    # 检测所有媒体
    result = detector.detect_from_html(html_content, "https://example.com")
    
    print(f"📋 总共找到 {result.total_found} 个媒体文件")
    
    # 过滤：只要视频
    video_only = detector.filter_media(result.media_list, media_types=['video'])
    print(f"🎬 视频文件: {len(video_only)} 个")
    for media in video_only:
        print(f"   {media.url} - {media.title}")
    
    # 过滤：只要音频
    audio_only = detector.filter_media(result.media_list, media_types=['audio'])
    print(f"🎵 音频文件: {len(audio_only)} 个")
    for media in audio_only:
        print(f"   {media.url} - {media.title}")
    
    # 过滤：包含关键词的媒体
    keyword_filtered = detector.filter_media(result.media_list, keywords=['教程', '演示'])
    print(f"🔍 包含关键词的文件: {len(keyword_filtered)} 个")
    for media in keyword_filtered:
        print(f"   {media.url} - {media.title}")
    
    detector.close()
    print()

def example_4_export_results():
    """示例4：导出结果"""
    print("💾 示例4：导出结果")
    print("=" * 40)
    
    # 创建检测器
    detector = MediaDetector()
    
    # 简单的测试数据
    html_content = """
    <html>
    <body>
        <video src="https://example.com/demo.mp4" title="演示视频"></video>
        <audio src="https://example.com/bgm.mp3" title="背景音乐"></audio>
        <img src="https://example.com/cover.jpg" alt="封面图片" title="视频封面">
    </body>
    </html>
    """
    
    # 检测媒体
    result = detector.detect_from_html(html_content, "https://example.com")
    
    print(f"📋 检测到 {result.total_found} 个媒体文件")
    
    try:
        # 导出为JSON
        json_file = detector.export_results(result, 'json', 'example_results')
        print(f"✅ JSON文件已保存: {json_file}")
        
        # 导出为CSV
        csv_file = detector.export_results(result, 'csv', 'example_results')
        print(f"✅ CSV文件已保存: {csv_file}")
        
        # 导出为Excel
        excel_file = detector.export_results(result, 'excel', 'example_results')
        print(f"✅ Excel文件已保存: {excel_file}")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
    
    detector.close()
    print()

def example_5_configuration():
    """示例5：配置管理"""
    print("⚙️  示例5：配置管理")
    print("=" * 40)
    
    # 创建自定义配置
    config = Config()
    
    # 修改网络配置
    config.update_config('network', 
                        timeout=60,
                        max_retries=5,
                        max_concurrent_downloads=3)
    
    # 修改爬虫配置
    config.update_config('crawler',
                        enable_javascript=True,
                        headless=True,
                        request_delay=2.0)
    
    # 修改下载配置
    config.update_config('download',
                        enable_download=False,
                        download_path="./my_downloads",
                        create_subdirs=True)
    
    print("📋 当前配置:")
    print(f"   网络超时: {config.network.timeout} 秒")
    print(f"   最大重试: {config.network.max_retries} 次")
    print(f"   启用JavaScript: {config.crawler.enable_javascript}")
    print(f"   请求延迟: {config.crawler.request_delay} 秒")
    print(f"   下载功能: {config.download.enable_download}")
    print(f"   下载路径: {config.download.download_path}")
    
    # 保存配置
    config.save_config()
    print("✅ 配置已保存到 config.json")
    print()

def main():
    """主函数"""
    print("🎯 网页多媒体嗅探程序使用示例")
    print("=" * 50)
    print()
    
    try:
        example_1_basic_detection()
        example_2_url_detection()
        example_3_media_filtering()
        example_4_export_results()
        example_5_configuration()
        
        print("🎉 所有示例运行完成！")
        print()
        print("💡 提示:")
        print("   - 在PowerShell中使用时，URL需要用引号包围")
        print("   - 使用 --dynamic 参数可以检测JavaScript动态内容")
        print("   - 使用 --download 参数可以下载检测到的媒体文件")
        print("   - 查看 PowerShell使用指南.md 了解更多用法")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
