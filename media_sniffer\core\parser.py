"""
HTML解析核心模块
================

解析HTML内容，提取媒体链接和相关信息
"""

import re
import logging
from typing import List, Dict, Set, Optional, Tuple
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup, Tag
from ..utils.media_types import MediaTypes, MediaType

class MediaInfo:
    """媒体信息类"""
    
    def __init__(self, url: str, media_type: MediaType, **kwargs):
        self.url = url
        self.media_type = media_type
        self.title = kwargs.get('title', '')
        self.description = kwargs.get('description', '')
        self.thumbnail = kwargs.get('thumbnail', '')
        self.duration = kwargs.get('duration', '')
        self.file_size = kwargs.get('file_size', '')
        self.quality = kwargs.get('quality', '')
        self.source_page = kwargs.get('source_page', '')
        self.mime_type = kwargs.get('mime_type', '')
        self.width = kwargs.get('width', '')
        self.height = kwargs.get('height', '')
        self.alt_text = kwargs.get('alt_text', '')
        self.metadata = kwargs.get('metadata', {})
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'url': self.url,
            'type': self.media_type.value,
            'title': self.title,
            'description': self.description,
            'thumbnail': self.thumbnail,
            'duration': self.duration,
            'file_size': self.file_size,
            'quality': self.quality,
            'source_page': self.source_page,
            'mime_type': self.mime_type,
            'width': self.width,
            'height': self.height,
            'alt_text': self.alt_text,
            'metadata': self.metadata
        }

class HTMLParser:
    """HTML解析器"""
    
    def __init__(self, config=None):
        """初始化HTML解析器"""
        self.config = config
        self.found_urls = set()
        
        # 媒体标签选择器
        self.media_selectors = {
            'video': ['video', 'source[src]', 'embed[src]', 'object[data]'],
            'audio': ['audio', 'source[src]'],
            'image': ['img[src]', 'picture source[srcset]', 'img[data-src]', 'img[data-original]']
        }
        
        # 链接选择器
        self.link_selectors = [
            'a[href]',
            'link[href]',
            'iframe[src]',
            'frame[src]'
        ]
    
    def parse_html(self, html_content: str, base_url: str) -> List[MediaInfo]:
        """解析HTML内容"""
        try:
            soup = BeautifulSoup(html_content, 'lxml')
            media_list = []
            
            # 解析直接的媒体标签
            media_list.extend(self._parse_media_tags(soup, base_url))
            
            # 解析链接中的媒体
            media_list.extend(self._parse_media_links(soup, base_url))
            
            # 解析JavaScript中的媒体URL
            media_list.extend(self._parse_javascript_media(soup, base_url))
            
            # 解析CSS中的媒体URL
            media_list.extend(self._parse_css_media(soup, base_url))
            
            # 去重
            unique_media = self._deduplicate_media(media_list)
            
            logging.info(f"从HTML中解析出 {len(unique_media)} 个媒体文件")
            return unique_media
            
        except Exception as e:
            logging.error(f"HTML解析失败: {e}")
            return []
    
    def _parse_media_tags(self, soup: BeautifulSoup, base_url: str) -> List[MediaInfo]:
        """解析媒体标签"""
        media_list = []
        
        # 解析视频标签
        for selector in self.media_selectors['video']:
            elements = soup.select(selector)
            for element in elements:
                media_info = self._extract_video_info(element, base_url)
                if media_info:
                    media_list.append(media_info)
        
        # 解析音频标签
        for selector in self.media_selectors['audio']:
            elements = soup.select(selector)
            for element in elements:
                media_info = self._extract_audio_info(element, base_url)
                if media_info:
                    media_list.append(media_info)
        
        # 解析图片标签
        for selector in self.media_selectors['image']:
            elements = soup.select(selector)
            for element in elements:
                media_info = self._extract_image_info(element, base_url)
                if media_info:
                    media_list.append(media_info)
        
        return media_list
    
    def _extract_video_info(self, element: Tag, base_url: str) -> Optional[MediaInfo]:
        """提取视频信息"""
        url = None
        
        # 获取视频URL
        if element.name == 'video':
            url = element.get('src')
            if not url:
                # 查找source标签
                source = element.find('source')
                if source:
                    url = source.get('src')
        elif element.name == 'source':
            url = element.get('src')
        elif element.name in ['embed', 'object']:
            url = element.get('src') or element.get('data')
        
        if not url:
            return None
        
        # 转换为绝对URL
        absolute_url = urljoin(base_url, url)
        
        # 检查是否为媒体URL
        if not MediaTypes.is_media_url(absolute_url):
            return None
        
        # 提取元数据
        title = element.get('title', '') or element.get('alt', '')
        width = element.get('width', '')
        height = element.get('height', '')
        poster = element.get('poster', '')
        
        if poster:
            poster = urljoin(base_url, poster)
        
        return MediaInfo(
            url=absolute_url,
            media_type=MediaType.VIDEO,
            title=title,
            width=width,
            height=height,
            thumbnail=poster,
            source_page=base_url
        )
    
    def _extract_audio_info(self, element: Tag, base_url: str) -> Optional[MediaInfo]:
        """提取音频信息"""
        url = None
        
        # 获取音频URL
        if element.name == 'audio':
            url = element.get('src')
            if not url:
                # 查找source标签
                source = element.find('source')
                if source:
                    url = source.get('src')
        elif element.name == 'source':
            url = element.get('src')
        
        if not url:
            return None
        
        # 转换为绝对URL
        absolute_url = urljoin(base_url, url)
        
        # 检查是否为媒体URL
        if not MediaTypes.is_media_url(absolute_url):
            return None
        
        # 提取元数据
        title = element.get('title', '') or element.get('alt', '')
        
        return MediaInfo(
            url=absolute_url,
            media_type=MediaType.AUDIO,
            title=title,
            source_page=base_url
        )
    
    def _extract_image_info(self, element: Tag, base_url: str) -> Optional[MediaInfo]:
        """提取图片信息"""
        url = None
        
        # 获取图片URL
        if element.name == 'img':
            url = (element.get('src') or 
                   element.get('data-src') or 
                   element.get('data-original') or
                   element.get('data-lazy'))
        elif element.name == 'source':
            srcset = element.get('srcset')
            if srcset:
                # 解析srcset，取第一个URL
                urls = re.findall(r'(\S+)', srcset)
                if urls:
                    url = urls[0]
        
        if not url:
            return None
        
        # 转换为绝对URL
        absolute_url = urljoin(base_url, url)
        
        # 检查是否为媒体URL
        if not MediaTypes.is_media_url(absolute_url):
            return None
        
        # 提取元数据
        title = element.get('title', '')
        alt_text = element.get('alt', '')
        width = element.get('width', '')
        height = element.get('height', '')
        
        return MediaInfo(
            url=absolute_url,
            media_type=MediaType.IMAGE,
            title=title,
            alt_text=alt_text,
            width=width,
            height=height,
            source_page=base_url
        )
    
    def _parse_media_links(self, soup: BeautifulSoup, base_url: str) -> List[MediaInfo]:
        """解析链接中的媒体"""
        media_list = []
        
        for selector in self.link_selectors:
            elements = soup.select(selector)
            for element in elements:
                url = element.get('href') or element.get('src')
                if not url:
                    continue
                
                absolute_url = urljoin(base_url, url)
                
                # 检查是否为媒体URL
                if MediaTypes.is_media_url(absolute_url):
                    media_type = MediaTypes.get_media_type_by_extension(absolute_url)
                    
                    if media_type != MediaType.UNKNOWN:
                        title = element.get('title', '') or element.get_text(strip=True)
                        
                        media_info = MediaInfo(
                            url=absolute_url,
                            media_type=media_type,
                            title=title,
                            source_page=base_url
                        )
                        media_list.append(media_info)
        
        return media_list
    
    def _parse_javascript_media(self, soup: BeautifulSoup, base_url: str) -> List[MediaInfo]:
        """解析JavaScript中的媒体URL"""
        media_list = []
        
        # 查找所有script标签
        scripts = soup.find_all('script')
        
        for script in scripts:
            if not script.string:
                continue
            
            # 使用正则表达式查找URL
            url_patterns = [
                r'["\']([^"\']*\.(?:mp4|avi|mkv|mov|wmv|flv|webm|m4v|mp3|wav|flac|aac|ogg|jpg|jpeg|png|gif|bmp|webp))["\']',
                r'src["\s]*:["\s]*["\']([^"\']+)["\']',
                r'url["\s]*:["\s]*["\']([^"\']+)["\']'
            ]
            
            for pattern in url_patterns:
                matches = re.findall(pattern, script.string, re.IGNORECASE)
                for match in matches:
                    absolute_url = urljoin(base_url, match)
                    
                    if MediaTypes.is_media_url(absolute_url):
                        media_type = MediaTypes.get_media_type_by_extension(absolute_url)
                        
                        if media_type != MediaType.UNKNOWN:
                            media_info = MediaInfo(
                                url=absolute_url,
                                media_type=media_type,
                                source_page=base_url
                            )
                            media_list.append(media_info)
        
        return media_list
    
    def _parse_css_media(self, soup: BeautifulSoup, base_url: str) -> List[MediaInfo]:
        """解析CSS中的媒体URL"""
        media_list = []
        
        # 查找所有style标签
        styles = soup.find_all('style')
        
        for style in styles:
            if not style.string:
                continue
            
            # 查找background-image等CSS属性中的URL
            url_pattern = r'url\(["\']?([^"\')\s]+)["\']?\)'
            matches = re.findall(url_pattern, style.string, re.IGNORECASE)
            
            for match in matches:
                absolute_url = urljoin(base_url, match)
                
                if MediaTypes.is_media_url(absolute_url):
                    media_type = MediaTypes.get_media_type_by_extension(absolute_url)
                    
                    if media_type == MediaType.IMAGE:
                        media_info = MediaInfo(
                            url=absolute_url,
                            media_type=media_type,
                            source_page=base_url
                        )
                        media_list.append(media_info)
        
        return media_list
    
    def _deduplicate_media(self, media_list: List[MediaInfo]) -> List[MediaInfo]:
        """去重媒体列表"""
        seen_urls = set()
        unique_media = []
        
        for media in media_list:
            if media.url not in seen_urls:
                seen_urls.add(media.url)
                unique_media.append(media)
        
        return unique_media
