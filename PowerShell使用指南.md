# PowerShell 使用指南

## 重要提示

在PowerShell中使用本程序时，包含特殊字符（如 `&`）的URL必须用引号包围。

## 正确的使用方法

### 1. 基础检测（静态内容）

```powershell
# 检测网页中的所有媒体文件
python main.py "https://example.com"

# 检测特定类型的媒体
python main.py "https://example.com" --types video audio

# 导出为Excel格式
python main.py "https://example.com" --output excel --file results
```

### 2. 动态内容检测（使用浏览器）

```powershell
# 启用动态检测（推荐用于现代网站）
python main.py "https://www.w3schools.com/html/html5_video.asp" --dynamic

# 禁用动态检测（仅静态内容）
python main.py "https://example.com" --no-dynamic
```

### 3. YouTube等视频网站

```powershell
# YouTube视频检测（注意：需要引号包围URL）
python main.py "https://www.youtube.com/watch?v=VIDEO_ID"

# 包含参数的YouTube URL
python main.py "https://www.youtube.com/watch?v=N9XS11kxeU4&list=RDN9XS11kxeU4&start_radio=1"

# Bilibili视频检测
python main.py "https://www.bilibili.com/video/BV1xx411c7mD"
```

### 4. 下载功能

```powershell
# 启用下载功能
python main.py "https://example.com" --download

# 指定下载路径
python main.py "https://example.com" --download --download-path "D:\Downloads\Media"

# 限制文件大小（字节）
python main.py "https://example.com" --download --max-size 10485760
```

### 5. 批量处理

```powershell
# 创建URL列表文件
@"
https://www.w3schools.com/html/html5_video.asp
https://www.w3schools.com/html/html5_audio.asp
https://example.com/page1
https://example.com/page2
"@ | Out-File -FilePath urls.txt -Encoding UTF8

# 批量处理
python main.py --batch urls.txt --output excel --file batch_results
```

### 6. 高级选项

```powershell
# 使用代理
python main.py "https://example.com" --proxy "http://proxy.example.com:8080"

# 自定义超时时间
python main.py "https://example.com" --timeout 60

# 启用详细日志
python main.py "https://example.com" --log-level DEBUG --log-file debug.log

# 自定义配置文件
python main.py "https://example.com" --config custom_config.json
```

## 常见错误和解决方案

### 错误1：不允许使用与号(&)

**错误信息：**
```
不允许使用与号(&)。& 运算符是为将来使用而保留的
```

**解决方案：**
用双引号包围整个URL：
```powershell
# ❌ 错误
python main.py https://www.youtube.com/watch?v=123&list=456

# ✅ 正确
python main.py "https://www.youtube.com/watch?v=123&list=456"
```

### 错误2：YouTube需要身份验证

**错误信息：**
```
Sign in to confirm you're not a bot
```

**解决方案：**
1. 使用动态检测模式：
```powershell
python main.py "https://www.youtube.com/watch?v=VIDEO_ID" --dynamic
```

2. 或者尝试其他视频网站进行测试

### 错误3：模块未找到

**错误信息：**
```
ModuleNotFoundError: No module named 'xxx'
```

**解决方案：**
安装依赖包：
```powershell
pip install -r requirements.txt
```

## 测试示例

### 测试1：基础功能
```powershell
python test_basic.py
```

### 测试2：功能演示
```powershell
python demo.py
```

### 测试3：实际网站检测
```powershell
# W3Schools视频教程页面（包含示例视频）
python main.py "https://www.w3schools.com/html/html5_video.asp" --dynamic

# 本地测试HTML文件
python main.py "file://$(Get-Location)\test_media.html"
```

## 输出文件

程序会生成以下文件：
- `media_results_[timestamp].json` - JSON格式结果
- `media_results_[timestamp].csv` - CSV格式结果  
- `media_results_[timestamp].xlsx` - Excel格式结果

## 性能优化建议

1. **对于简单网站**：使用 `--no-dynamic` 提高速度
2. **对于现代网站**：使用 `--dynamic` 获取完整内容
3. **批量处理**：使用 `--batch` 处理多个URL
4. **大文件过滤**：使用 `--max-size` 限制文件大小

## 故障排除

如果遇到问题，可以：

1. 启用详细日志：
```powershell
python main.py "https://example.com" --log-level DEBUG
```

2. 检查网络连接：
```powershell
python main.py "https://httpbin.org/html" --no-dynamic
```

3. 测试本地文件：
```powershell
python main.py "file://$(Get-Location)\test_media.html"
```

4. 查看帮助信息：
```powershell
python main.py --help
```
