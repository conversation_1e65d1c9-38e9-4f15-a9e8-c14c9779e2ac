#!/usr/bin/env python3
"""
网页多媒体嗅探程序演示
======================

演示程序的主要功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from media_sniffer import MediaDetector
from media_sniffer.utils.config import Config

def demo_html_parsing():
    """演示HTML解析功能"""
    print("🔍 演示1: HTML内容解析")
    print("=" * 50)
    
    # 测试HTML内容
    test_html = """
    <html>
    <head><title>测试页面</title></head>
    <body>
        <h1>媒体内容测试</h1>
        
        <!-- 视频 -->
        <video src="https://example.com/video.mp4" title="示例视频" poster="https://example.com/poster.jpg"></video>
        <video controls>
            <source src="https://example.com/video.webm" type="video/webm">
            <source src="https://example.com/video.mp4" type="video/mp4">
        </video>
        
        <!-- 音频 -->
        <audio src="https://example.com/audio.mp3" title="示例音频"></audio>
        <audio controls>
            <source src="https://example.com/audio.wav" type="audio/wav">
            <source src="https://example.com/audio.mp3" type="audio/mpeg">
        </audio>
        
        <!-- 图片 -->
        <img src="https://example.com/image1.jpg" alt="图片1" title="示例图片1">
        <img src="https://example.com/image2.png" alt="图片2">
        <img data-src="https://example.com/lazy.jpg" alt="懒加载图片">
        
        <!-- 链接 -->
        <a href="https://example.com/download.mp4" title="下载视频">视频下载</a>
        <a href="https://example.com/music.mp3">音乐下载</a>
        <a href="https://example.com/photo.jpg">查看照片</a>
        
        <!-- 嵌入内容 -->
        <embed src="https://example.com/flash.swf" type="application/x-shockwave-flash">
        <object data="https://example.com/media.mp4" type="video/mp4"></object>
        
        <!-- JavaScript中的URL -->
        <script>
            var videoSrc = "https://example.com/js-video.mp4";
            var audioSrc = "https://example.com/js-audio.mp3";
            var imageSrc = "https://example.com/js-image.jpg";
        </script>
        
        <!-- CSS中的背景图 -->
        <style>
            .bg { background-image: url('https://example.com/bg.jpg'); }
            .hero { background: url("https://example.com/hero.png") no-repeat; }
        </style>
    </body>
    </html>
    """
    
    # 创建检测器
    config = Config()
    detector = MediaDetector(config)
    
    # 解析HTML
    result = detector.detect_from_html(test_html, "https://example.com")
    
    print(f"✅ 检测完成！找到 {result.total_found} 个媒体文件")
    print(f"⏱️  处理时间: {result.processing_time:.3f} 秒")
    print()
    
    # 按类型统计
    print("📊 按类型分布:")
    for media_type, count in result.by_type.items():
        if count > 0:
            print(f"   {media_type.capitalize()}: {count} 个")
    print()
    
    # 显示详细列表
    print("📋 媒体文件列表:")
    for i, media in enumerate(result.media_list[:10], 1):  # 只显示前10个
        print(f"   {i}. [{media.media_type.value.upper()}] {media.url}")
        if media.title:
            print(f"      标题: {media.title}")
        if media.alt_text:
            print(f"      描述: {media.alt_text}")
    
    if len(result.media_list) > 10:
        print(f"   ... 还有 {len(result.media_list) - 10} 个文件")
    
    detector.close()
    print()

def demo_media_types():
    """演示媒体类型识别"""
    print("🎯 演示2: 媒体类型识别")
    print("=" * 50)
    
    from media_sniffer.utils.media_types import MediaTypes
    
    test_urls = [
        "https://example.com/video.mp4",
        "https://example.com/movie.avi", 
        "https://example.com/clip.webm",
        "https://example.com/song.mp3",
        "https://example.com/audio.wav",
        "https://example.com/music.flac",
        "https://example.com/photo.jpg",
        "https://example.com/image.png",
        "https://example.com/pic.gif",
        "https://youtube.com/watch?v=dQw4w9WgXcQ",
        "https://bilibili.com/video/BV1xx411c7mD",
        "https://example.com/document.pdf",
        "https://example.com/archive.zip"
    ]
    
    print("🔍 URL类型识别测试:")
    for url in test_urls:
        media_type = MediaTypes.get_media_type_by_extension(url)
        is_media = MediaTypes.is_media_url(url)
        is_video_site, site_name = MediaTypes.is_video_site_url(url)
        
        status = "✅" if is_media else "❌"
        print(f"   {status} {url}")
        print(f"      类型: {media_type.value}")
        if is_video_site:
            print(f"      特殊网站: {site_name}")
    print()

def demo_config():
    """演示配置管理"""
    print("⚙️  演示3: 配置管理")
    print("=" * 50)
    
    # 创建默认配置
    config = Config()
    
    print("📋 当前配置:")
    print(f"   网络超时: {config.network.timeout} 秒")
    print(f"   最大重试: {config.network.max_retries} 次")
    print(f"   并发下载: {config.network.max_concurrent_downloads} 个")
    print(f"   启用JavaScript: {config.crawler.enable_javascript}")
    print(f"   无头模式: {config.crawler.headless}")
    print(f"   用户代理轮换: {config.crawler.user_agent_rotation}")
    print(f"   下载功能: {config.download.enable_download}")
    print(f"   下载路径: {config.download.download_path}")
    print(f"   创建子目录: {config.download.create_subdirs}")
    print(f"   输出格式: {config.output.output_format}")
    print(f"   日志级别: {config.log.log_level}")
    print()
    
    # 修改配置
    print("🔧 修改配置示例:")
    config.update_config('network', timeout=60, max_retries=5)
    config.update_config('download', enable_download=True, download_path="./my_downloads")
    
    print(f"   网络超时: {config.network.timeout} 秒 (已修改)")
    print(f"   最大重试: {config.network.max_retries} 次 (已修改)")
    print(f"   下载功能: {config.download.enable_download} (已修改)")
    print(f"   下载路径: {config.download.download_path} (已修改)")
    print()

def demo_supported_formats():
    """演示支持的格式"""
    print("📁 演示4: 支持的媒体格式")
    print("=" * 50)
    
    from media_sniffer.utils.media_types import SUPPORTED_FORMATS
    
    for category, extensions in SUPPORTED_FORMATS.items():
        print(f"📂 {category.upper()} 格式 ({len(extensions)} 种):")
        # 按字母顺序排序并分行显示
        sorted_exts = sorted(extensions)
        for i in range(0, len(sorted_exts), 8):  # 每行8个
            line_exts = sorted_exts[i:i+8]
            print(f"   {', '.join(line_exts)}")
        print()

def main():
    """主演示函数"""
    print("🎉 网页多媒体嗅探程序功能演示")
    print("=" * 60)
    print()
    
    try:
        # 运行各个演示
        demo_html_parsing()
        demo_media_types()
        demo_config()
        demo_supported_formats()
        
        print("✨ 演示完成！")
        print()
        print("🚀 快速开始:")
        print("   python main.py https://example.com")
        print("   python main.py https://example.com --dynamic --download")
        print("   python main.py https://example.com --output excel --file results")
        print()
        print("📖 更多信息请查看 README.md")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
