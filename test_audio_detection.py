#!/usr/bin/env python3
"""
音频检测测试脚本
================

测试修复后的音频检测功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from media_sniffer.utils.media_types import MediaTypes, MediaType

def test_m4s_detection():
    """测试.m4s文件检测"""
    print("🎵 测试.m4s文件类型检测")
    print("=" * 40)
    
    test_urls = [
        "https://example.com/audio.m4s",
        "https://xy39x174x253x210xy.mcdn.bilivideo.cn:8082/v1/resource/25827875444-1-100050.m4s",
        "https://example.com/video.mp4",
        "https://example.com/music.mp3",
        "https://example.com/image.jpg"
    ]
    
    for url in test_urls:
        media_type = MediaTypes.get_media_type_by_extension(url)
        is_media = MediaTypes.is_media_url(url)
        
        print(f"📁 {url}")
        print(f"   类型: {media_type.value}")
        print(f"   是媒体: {'✅' if is_media else '❌'}")
        print()

def test_bilibili_formats():
    """测试Bilibili格式识别"""
    print("📺 测试Bilibili相关格式")
    print("=" * 40)
    
    # 模拟Bilibili的不同格式
    test_formats = [
        {
            'url': 'https://example.com/video.m4s',
            'vcodec': 'h264',
            'acodec': 'none',
            'height': 1080,
            'description': '仅视频流'
        },
        {
            'url': 'https://example.com/audio.m4s', 
            'vcodec': 'none',
            'acodec': 'aac',
            'abr': 128,
            'description': '仅音频流'
        },
        {
            'url': 'https://example.com/combined.mp4',
            'vcodec': 'h264',
            'acodec': 'aac',
            'height': 720,
            'abr': 128,
            'description': '视频+音频'
        }
    ]
    
    for fmt in test_formats:
        url = fmt['url']
        media_type = MediaTypes.get_media_type_by_extension(url)
        
        print(f"📁 {url}")
        print(f"   描述: {fmt['description']}")
        print(f"   检测类型: {media_type.value}")
        print(f"   视频编码: {fmt.get('vcodec', 'none')}")
        print(f"   音频编码: {fmt.get('acodec', 'none')}")
        
        # 根据编码判断实际类型
        has_video = fmt.get('vcodec') != 'none'
        has_audio = fmt.get('acodec') != 'none'
        
        if has_video and has_audio:
            actual_type = "视频(含音频)"
        elif has_video:
            actual_type = "视频(无音频)"
        elif has_audio:
            actual_type = "音频"
        else:
            actual_type = "未知"
        
        print(f"   实际类型: {actual_type}")
        print()

def test_audio_extensions():
    """测试音频扩展名支持"""
    print("🎶 测试音频扩展名支持")
    print("=" * 40)
    
    audio_extensions = MediaTypes.AUDIO_EXTENSIONS
    print(f"支持的音频格式 ({len(audio_extensions)} 种):")
    
    # 按字母顺序排序并分行显示
    sorted_exts = sorted(audio_extensions)
    for i in range(0, len(sorted_exts), 8):  # 每行8个
        line_exts = sorted_exts[i:i+8]
        print(f"   {', '.join(line_exts)}")
    
    print()
    
    # 检查是否包含m4s
    if 'm4s' in audio_extensions:
        print("✅ m4s格式已包含在音频扩展名中")
    else:
        print("❌ m4s格式未包含在音频扩展名中")
    print()

def test_html_audio_detection():
    """测试HTML中的音频检测"""
    print("🔍 测试HTML音频检测")
    print("=" * 40)
    
    from media_sniffer.core.parser import HTMLParser
    
    # 包含各种音频格式的HTML
    test_html = """
    <html>
    <body>
        <h1>音频测试</h1>
        
        <!-- 标准音频标签 -->
        <audio src="https://example.com/music.mp3" controls title="MP3音乐"></audio>
        <audio controls>
            <source src="https://example.com/audio.m4s" type="audio/mp4">
            <source src="https://example.com/audio.wav" type="audio/wav">
        </audio>
        
        <!-- 音频链接 -->
        <a href="https://example.com/song.flac" title="FLAC音乐">下载FLAC</a>
        <a href="https://example.com/podcast.m4a">播客</a>
        
        <!-- JavaScript中的音频URL -->
        <script>
            var audioUrl = "https://example.com/bgm.ogg";
            var musicSrc = "https://example.com/theme.aac";
        </script>
        
        <!-- 视频文件（对比） -->
        <video src="https://example.com/video.mp4" controls></video>
        <img src="https://example.com/image.jpg" alt="图片">
    </body>
    </html>
    """
    
    parser = HTMLParser()
    media_list = parser.parse_html(test_html, "https://example.com")
    
    print(f"检测到 {len(media_list)} 个媒体文件:")
    
    audio_count = 0
    video_count = 0
    image_count = 0
    
    for i, media in enumerate(media_list, 1):
        print(f"{i}. [{media.media_type.value.upper()}] {media.url}")
        if media.title:
            print(f"   标题: {media.title}")
        
        if media.media_type == MediaType.AUDIO:
            audio_count += 1
        elif media.media_type == MediaType.VIDEO:
            video_count += 1
        elif media.media_type == MediaType.IMAGE:
            image_count += 1
    
    print()
    print(f"统计: 音频 {audio_count} 个, 视频 {video_count} 个, 图片 {image_count} 个")
    
    if audio_count > 0:
        print("✅ 音频检测正常")
    else:
        print("❌ 未检测到音频文件")
    print()

def main():
    """主测试函数"""
    print("🧪 音频检测功能测试")
    print("=" * 50)
    print()
    
    try:
        test_m4s_detection()
        test_bilibili_formats()
        test_audio_extensions()
        test_html_audio_detection()
        
        print("🎉 所有测试完成！")
        print()
        print("💡 修复说明:")
        print("   - 已将 m4s 格式添加到音频扩展名列表")
        print("   - 改进了Bilibili视频处理，支持分离音频和视频流")
        print("   - 现在可以正确识别音频流文件")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
