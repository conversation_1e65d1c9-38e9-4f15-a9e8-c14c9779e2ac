<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试媒体页面</title>
</head>
<body>
    <h1>测试媒体内容</h1>
    
    <!-- 视频内容 -->
    <h2>视频</h2>
    <video src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" controls>
        您的浏览器不支持视频标签。
    </video>
    
    <video controls>
        <source src="https://sample-videos.com/zip/10/webm/SampleVideo_1280x720_1mb.webm" type="video/webm">
        <source src="https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4" type="video/mp4">
    </video>
    
    <!-- 音频内容 -->
    <h2>音频</h2>
    <audio src="https://sample-videos.com/zip/10/mp3/SampleAudio_0.4mb.mp3" controls>
        您的浏览器不支持音频标签。
    </audio>
    
    <audio controls>
        <source src="https://sample-videos.com/zip/10/wav/SampleAudio_0.4mb.wav" type="audio/wav">
        <source src="https://sample-videos.com/zip/10/mp3/SampleAudio_0.7mb.mp3" type="audio/mpeg">
    </audio>
    
    <!-- 图片内容 -->
    <h2>图片</h2>
    <img src="https://picsum.photos/800/600" alt="随机图片1" title="测试图片1">
    <img src="https://picsum.photos/400/300" alt="随机图片2" title="测试图片2">
    <img src="https://via.placeholder.com/500x400.jpg" alt="占位图片">
    
    <!-- 链接中的媒体 -->
    <h2>媒体链接</h2>
    <a href="https://sample-videos.com/zip/10/mp4/SampleVideo_720x480_1mb.mp4">下载视频</a><br>
    <a href="https://sample-videos.com/zip/10/mp3/SampleAudio_0.7mb.mp3">下载音频</a><br>
    <a href="https://picsum.photos/1200/800.jpg">查看大图</a><br>
    
    <!-- 嵌入内容 -->
    <h2>嵌入内容</h2>
    <embed src="https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_1mb.mp4" width="360" height="240">
    
    <!-- 背景图片 -->
    <div style="background-image: url('https://picsum.photos/1920/1080'); width: 200px; height: 150px; background-size: cover;">
        背景图片测试
    </div>
    
    <!-- JavaScript中的媒体URL -->
    <script>
        var videoUrl = "https://sample-videos.com/zip/10/mp4/SampleVideo_480x360_1mb.mp4";
        var audioUrl = "https://sample-videos.com/zip/10/mp3/SampleAudio_0.4mb.mp3";
        var imageUrl = "https://picsum.photos/600/400.png";
        
        console.log("Video URL:", videoUrl);
        console.log("Audio URL:", audioUrl);
        console.log("Image URL:", imageUrl);
    </script>
</body>
</html>
