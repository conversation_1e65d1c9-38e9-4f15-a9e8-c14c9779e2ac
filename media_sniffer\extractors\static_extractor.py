"""
静态内容提取器
==============

处理静态HTML页面的媒体内容提取
"""

import logging
from typing import List, Optional
import requests
from ..core.parser import HTMLParser, MediaInfo
from ..utils.anti_crawler import AntiCrawler

class StaticExtractor:
    """静态内容提取器"""
    
    def __init__(self, config=None):
        """初始化静态提取器"""
        self.config = config
        self.parser = HTMLParser(config)
        self.anti_crawler = AntiCrawler(config)
        
    def extract_from_url(self, url: str) -> List[MediaInfo]:
        """从URL提取媒体内容"""
        try:
            logging.info(f"开始提取静态内容: {url}")
            
            # 获取页面内容
            response = self.anti_crawler.make_request(url)
            
            # 检查是否被阻止
            if self.anti_crawler.is_blocked(response):
                logging.warning(f"请求被阻止: {url}")
                return []
            
            # 解析HTML内容
            media_list = self.parser.parse_html(response.text, url)
            
            # 增强媒体信息
            enhanced_media = self._enhance_media_info(media_list, response)
            
            logging.info(f"静态提取完成，找到 {len(enhanced_media)} 个媒体文件")
            return enhanced_media
            
        except requests.exceptions.RequestException as e:
            logging.error(f"请求失败: {url} - {e}")
            return []
        except Exception as e:
            logging.error(f"静态提取失败: {url} - {e}")
            return []
    
    def extract_from_html(self, html_content: str, base_url: str) -> List[MediaInfo]:
        """从HTML内容提取媒体"""
        try:
            logging.info(f"开始解析HTML内容，基础URL: {base_url}")
            
            # 解析HTML内容
            media_list = self.parser.parse_html(html_content, base_url)
            
            logging.info(f"HTML解析完成，找到 {len(media_list)} 个媒体文件")
            return media_list
            
        except Exception as e:
            logging.error(f"HTML解析失败: {e}")
            return []
    
    def _enhance_media_info(self, media_list: List[MediaInfo], response: requests.Response) -> List[MediaInfo]:
        """增强媒体信息"""
        enhanced_list = []
        
        for media in media_list:
            try:
                # 获取媒体文件的详细信息
                enhanced_media = self._get_media_details(media, response)
                enhanced_list.append(enhanced_media)
                
            except Exception as e:
                logging.warning(f"增强媒体信息失败: {media.url} - {e}")
                enhanced_list.append(media)
        
        return enhanced_list
    
    def _get_media_details(self, media: MediaInfo, page_response: requests.Response) -> MediaInfo:
        """获取媒体文件详细信息"""
        try:
            # 发送HEAD请求获取文件信息
            head_response = self.anti_crawler.make_request(media.url, method='HEAD')
            
            # 更新MIME类型
            content_type = head_response.headers.get('content-type', '')
            if content_type:
                media.mime_type = content_type.split(';')[0].strip()
            
            # 更新文件大小
            content_length = head_response.headers.get('content-length')
            if content_length:
                try:
                    size_bytes = int(content_length)
                    media.file_size = self._format_file_size(size_bytes)
                except ValueError:
                    pass
            
            # 从页面响应中提取额外信息
            self._extract_page_metadata(media, page_response)
            
        except Exception as e:
            logging.debug(f"获取媒体详情失败: {media.url} - {e}")
        
        return media
    
    def _extract_page_metadata(self, media: MediaInfo, response: requests.Response) -> None:
        """从页面中提取元数据"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'lxml')
            
            # 提取页面标题作为描述
            if not media.description:
                title_tag = soup.find('title')
                if title_tag:
                    media.description = title_tag.get_text(strip=True)
            
            # 提取meta描述
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc and not media.description:
                media.description = meta_desc.get('content', '')
            
            # 提取Open Graph信息
            og_title = soup.find('meta', attrs={'property': 'og:title'})
            if og_title and not media.title:
                media.title = og_title.get('content', '')
            
            og_desc = soup.find('meta', attrs={'property': 'og:description'})
            if og_desc and not media.description:
                media.description = og_desc.get('content', '')
            
            og_image = soup.find('meta', attrs={'property': 'og:image'})
            if og_image and not media.thumbnail:
                from urllib.parse import urljoin
                media.thumbnail = urljoin(response.url, og_image.get('content', ''))
            
        except Exception as e:
            logging.debug(f"提取页面元数据失败: {e}")
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def validate_media_urls(self, media_list: List[MediaInfo]) -> List[MediaInfo]:
        """验证媒体URL的有效性"""
        valid_media = []
        
        for media in media_list:
            try:
                # 发送HEAD请求验证URL
                response = self.anti_crawler.make_request(media.url, method='HEAD')
                
                if response.status_code == 200:
                    valid_media.append(media)
                    logging.debug(f"URL验证成功: {media.url}")
                else:
                    logging.warning(f"URL验证失败: {media.url} - 状态码: {response.status_code}")
                    
            except Exception as e:
                logging.warning(f"URL验证异常: {media.url} - {e}")
        
        logging.info(f"URL验证完成，有效媒体: {len(valid_media)}/{len(media_list)}")
        return valid_media
    
    def extract_with_filters(self, url: str, media_types: List[str] = None, 
                           min_size: int = 0, max_size: int = None) -> List[MediaInfo]:
        """带过滤条件的提取"""
        # 提取所有媒体
        all_media = self.extract_from_url(url)
        
        # 应用过滤器
        filtered_media = []
        
        for media in all_media:
            # 类型过滤
            if media_types and media.media_type.value not in media_types:
                continue
            
            # 大小过滤
            if media.file_size:
                try:
                    # 简单的大小解析
                    size_str = media.file_size.replace(' ', '').upper()
                    if 'KB' in size_str:
                        size_bytes = float(size_str.replace('KB', '')) * 1024
                    elif 'MB' in size_str:
                        size_bytes = float(size_str.replace('MB', '')) * 1024 * 1024
                    elif 'GB' in size_str:
                        size_bytes = float(size_str.replace('GB', '')) * 1024 * 1024 * 1024
                    else:
                        size_bytes = float(size_str.replace('B', ''))
                    
                    if size_bytes < min_size:
                        continue
                    if max_size and size_bytes > max_size:
                        continue
                        
                except (ValueError, AttributeError):
                    pass
            
            filtered_media.append(media)
        
        logging.info(f"过滤后媒体数量: {len(filtered_media)}")
        return filtered_media
    
    def get_session_info(self) -> dict:
        """获取会话信息"""
        return self.anti_crawler.get_session_info()
