"""
媒体类型定义模块
================

定义支持的媒体文件类型和识别规则
"""

import re
from enum import Enum
from typing import Dict, List, Set, Tuple

class MediaType(Enum):
    """媒体类型枚举"""
    VIDEO = "video"
    AUDIO = "audio"
    IMAGE = "image"
    UNKNOWN = "unknown"

class MediaTypes:
    """媒体类型管理类"""
    
    # 视频文件扩展名
    VIDEO_EXTENSIONS = {
        'mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v',
        'mpg', 'mpeg', '3gp', 'ogv', 'ts', 'm3u8', 'f4v', 'asf',
        'rm', 'rmvb', 'vob', 'divx', 'xvid'
    }
    
    # 音频文件扩展名
    AUDIO_EXTENSIONS = {
        'mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'opus',
        'ape', 'ac3', 'dts', 'amr', 'au', 'ra', 'aiff', 'mid',
        'midi', 'kar', 'mp2', 'mpa', 'm4s'
    }
    
    # 图片文件扩展名
    IMAGE_EXTENSIONS = {
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico',
        'tiff', 'tif', 'psd', 'raw', 'cr2', 'nef', 'orf', 'sr2',
        'heic', 'heif', 'avif', 'jxl'
    }
    
    # MIME类型映射
    MIME_TYPES = {
        # 视频MIME类型
        'video/mp4': MediaType.VIDEO,
        'video/avi': MediaType.VIDEO,
        'video/quicktime': MediaType.VIDEO,
        'video/x-msvideo': MediaType.VIDEO,
        'video/webm': MediaType.VIDEO,
        'video/ogg': MediaType.VIDEO,
        'video/3gpp': MediaType.VIDEO,
        'video/x-flv': MediaType.VIDEO,
        'application/x-mpegURL': MediaType.VIDEO,
        'application/vnd.apple.mpegurl': MediaType.VIDEO,
        
        # 音频MIME类型
        'audio/mpeg': MediaType.AUDIO,
        'audio/wav': MediaType.AUDIO,
        'audio/flac': MediaType.AUDIO,
        'audio/aac': MediaType.AUDIO,
        'audio/ogg': MediaType.AUDIO,
        'audio/webm': MediaType.AUDIO,
        'audio/x-ms-wma': MediaType.AUDIO,
        'audio/mp4': MediaType.AUDIO,
        
        # 图片MIME类型
        'image/jpeg': MediaType.IMAGE,
        'image/png': MediaType.IMAGE,
        'image/gif': MediaType.IMAGE,
        'image/bmp': MediaType.IMAGE,
        'image/webp': MediaType.IMAGE,
        'image/svg+xml': MediaType.IMAGE,
        'image/tiff': MediaType.IMAGE,
        'image/x-icon': MediaType.IMAGE,
        'image/heic': MediaType.IMAGE,
        'image/avif': MediaType.IMAGE,
    }
    
    # 常见视频网站的特殊URL模式
    VIDEO_SITE_PATTERNS = {
        'youtube': [
            r'youtube\.com/watch\?v=[\w-]+',
            r'youtu\.be/[\w-]+',
            r'youtube\.com/embed/[\w-]+',
        ],
        'bilibili': [
            r'bilibili\.com/video/[Bb][Vv][\w]+',
            r'b23\.tv/[\w]+',
        ],
        'vimeo': [
            r'vimeo\.com/\d+',
            r'player\.vimeo\.com/video/\d+',
        ],
        'dailymotion': [
            r'dailymotion\.com/video/[\w]+',
        ],
        'twitch': [
            r'twitch\.tv/videos/\d+',
            r'twitch\.tv/[\w]+',
        ]
    }
    
    @classmethod
    def get_media_type_by_extension(cls, url: str) -> MediaType:
        """根据文件扩展名判断媒体类型"""
        # 提取文件扩展名
        extension = cls._extract_extension(url)
        if not extension:
            return MediaType.UNKNOWN
            
        extension = extension.lower()
        
        if extension in cls.VIDEO_EXTENSIONS:
            return MediaType.VIDEO
        elif extension in cls.AUDIO_EXTENSIONS:
            return MediaType.AUDIO
        elif extension in cls.IMAGE_EXTENSIONS:
            return MediaType.IMAGE
        else:
            return MediaType.UNKNOWN
    
    @classmethod
    def get_media_type_by_mime(cls, mime_type: str) -> MediaType:
        """根据MIME类型判断媒体类型"""
        if not mime_type:
            return MediaType.UNKNOWN
        
        # 处理带参数的MIME类型
        mime_type = mime_type.split(';')[0].strip().lower()
        
        return cls.MIME_TYPES.get(mime_type, MediaType.UNKNOWN)
    
    @classmethod
    def is_video_site_url(cls, url: str) -> Tuple[bool, str]:
        """检查是否为已知视频网站URL"""
        url_lower = url.lower()
        
        for site_name, patterns in cls.VIDEO_SITE_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, url_lower):
                    return True, site_name
        
        return False, ""
    
    @classmethod
    def _extract_extension(cls, url: str) -> str:
        """从URL中提取文件扩展名"""
        # 移除查询参数和锚点
        url = url.split('?')[0].split('#')[0]
        
        # 提取扩展名
        if '.' in url:
            return url.split('.')[-1]
        
        return ""
    
    @classmethod
    def is_media_url(cls, url: str) -> bool:
        """判断URL是否可能是媒体文件"""
        # 检查扩展名
        media_type = cls.get_media_type_by_extension(url)
        if media_type != MediaType.UNKNOWN:
            return True
        
        # 检查是否为视频网站
        is_video_site, _ = cls.is_video_site_url(url)
        if is_video_site:
            return True
        
        # 检查URL中是否包含媒体相关关键词
        media_keywords = [
            'video', 'audio', 'image', 'media', 'stream', 'play',
            'watch', 'listen', 'download', 'file', 'content'
        ]
        
        url_lower = url.lower()
        for keyword in media_keywords:
            if keyword in url_lower:
                return True
        
        return False

# 支持的格式汇总
SUPPORTED_FORMATS = {
    'video': MediaTypes.VIDEO_EXTENSIONS,
    'audio': MediaTypes.AUDIO_EXTENSIONS,
    'image': MediaTypes.IMAGE_EXTENSIONS
}

# 所有支持的扩展名
ALL_EXTENSIONS = (
    MediaTypes.VIDEO_EXTENSIONS | 
    MediaTypes.AUDIO_EXTENSIONS | 
    MediaTypes.IMAGE_EXTENSIONS
)
