"""
网页多媒体嗅探程序
====================

一个功能全面的网页多媒体内容检测和下载工具。

主要功能：
- 检测网页中的视频、音频、图片等多媒体内容
- 支持静态和动态内容解析
- 支持多种输出格式
- 内置下载功能
- 反爬虫机制处理
- 特定网站优化支持

作者: AI Assistant
版本: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"
__email__ = ""
__description__ = "网页多媒体嗅探程序"

from .core.detector import MediaDetector
from .core.parser import HTMLParser
from .core.downloader import MediaDownloader

# 公共接口
__all__ = [
    'MediaDetector',
    'HTMLParser', 
    'MediaDownloader'
]

# 版本信息
VERSION_INFO = {
    'major': 1,
    'minor': 0,
    'patch': 0,
    'release': 'stable'
}

def get_version():
    """获取版本信息"""
    return f"{VERSION_INFO['major']}.{VERSION_INFO['minor']}.{VERSION_INFO['patch']}"

def get_full_version():
    """获取完整版本信息"""
    return f"{get_version()}-{VERSION_INFO['release']}"
