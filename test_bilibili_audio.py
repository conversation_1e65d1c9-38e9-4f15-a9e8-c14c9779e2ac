#!/usr/bin/env python3
"""
Bilibili音频检测测试
===================

测试修复后的Bilibili音频检测功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from media_sniffer.extractors.site_specific import SiteSpecificExtractor
from media_sniffer.utils.config import Config

def test_bilibili_detection():
    """测试Bilibili检测"""
    print("🎵 测试Bilibili音频检测")
    print("=" * 50)
    
    # 创建配置
    config = Config()
    extractor = SiteSpecificExtractor(config)
    
    # 测试URL
    test_url = "https://www.bilibili.com/video/BV1WigzztEyx/?spm_id_from=333.1007.tianma.1-1-1.click"
    
    print(f"🔍 测试URL: {test_url}")
    print()
    
    if not extractor.can_handle(test_url):
        print("❌ 无法处理此URL")
        return
    
    try:
        print("📋 1. 使用标准方法提取...")
        media_list = extractor.extract_from_url(test_url)
        
        print(f"   找到 {len(media_list)} 个媒体文件:")
        
        video_count = 0
        audio_count = 0
        
        for i, media in enumerate(media_list, 1):
            print(f"   {i}. [{media.media_type.value.upper()}] {media.title}")
            print(f"      URL: {media.url[:80]}...")
            print(f"      质量: {media.quality}")
            
            if media.metadata:
                format_id = media.metadata.get('format_id', '')
                vcodec = media.metadata.get('vcodec', '')
                acodec = media.metadata.get('acodec', '')
                
                if format_id:
                    print(f"      格式ID: {format_id}")
                if vcodec and vcodec != 'none':
                    print(f"      视频编码: {vcodec}")
                if acodec and acodec != 'none':
                    print(f"      音频编码: {acodec}")
            
            if media.media_type.value == 'video':
                video_count += 1
            elif media.media_type.value == 'audio':
                audio_count += 1
            
            print()
        
        print(f"📊 统计: 视频 {video_count} 个, 音频 {audio_count} 个")
        
        if audio_count > 0:
            print("✅ 成功检测到音频文件！")
        else:
            print("⚠️  未检测到音频文件")
        
        print()
        
        # 测试所有格式提取
        print("📋 2. 使用所有格式提取...")
        all_formats = extractor.extract_all_formats(test_url)
        
        print(f"   找到 {len(all_formats)} 个格式:")
        
        format_stats = {'video': 0, 'audio': 0, 'combined': 0}
        
        for i, media in enumerate(all_formats[:10], 1):  # 只显示前10个
            print(f"   {i}. [{media.media_type.value.upper()}] {media.title}")
            
            if media.metadata:
                vcodec = media.metadata.get('vcodec', 'none')
                acodec = media.metadata.get('acodec', 'none')
                format_id = media.metadata.get('format_id', '')
                
                print(f"      格式: {format_id}")
                print(f"      编码: V:{vcodec} A:{acodec}")
                
                if vcodec != 'none' and acodec != 'none':
                    format_stats['combined'] += 1
                elif vcodec != 'none':
                    format_stats['video'] += 1
                elif acodec != 'none':
                    format_stats['audio'] += 1
            
            print()
        
        if len(all_formats) > 10:
            print(f"   ... 还有 {len(all_formats) - 10} 个格式")
        
        print(f"📊 格式统计:")
        print(f"   视频+音频: {format_stats['combined']} 个")
        print(f"   仅视频: {format_stats['video']} 个")
        print(f"   仅音频: {format_stats['audio']} 个")
        
        if format_stats['audio'] > 0:
            print("✅ 找到音频流！")
        else:
            print("❌ 未找到独立的音频流")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_m4s_classification():
    """测试.m4s文件分类"""
    print("\n🎯 测试.m4s文件分类")
    print("=" * 50)
    
    from media_sniffer.utils.media_types import MediaTypes
    
    test_urls = [
        "https://example.com/video.m4s",
        "https://xy39x174x253x210xy.mcdn.bilivideo.cn:8082/v1/resource/25827875444-1-100050.m4s",
        "https://example.com/audio.m4a",
        "https://example.com/video.mp4"
    ]
    
    for url in test_urls:
        media_type = MediaTypes.get_media_type_by_extension(url)
        print(f"📁 {url}")
        print(f"   类型: {media_type.value}")
        print()

def main():
    """主函数"""
    print("🧪 Bilibili音频检测测试")
    print("=" * 60)
    
    try:
        test_bilibili_detection()
        test_m4s_classification()
        
        print("🎯 总结:")
        print("   - .m4s文件现在正确识别为视频格式")
        print("   - 改进了Bilibili处理器，支持提取所有格式")
        print("   - 如果仍未检测到音频，可能是因为:")
        print("     1. Bilibili使用了特殊的音频流格式")
        print("     2. 需要登录或特殊权限才能访问音频流")
        print("     3. 音频流在JavaScript中动态加载")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
