"""
配置管理模块
============

管理程序的配置参数和设置
"""

import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import logging

@dataclass
class NetworkConfig:
    """网络配置"""
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    max_concurrent_downloads: int = 5
    chunk_size: int = 8192
    use_proxy: bool = False
    proxy_url: Optional[str] = None
    verify_ssl: bool = True

@dataclass
class CrawlerConfig:
    """爬虫配置"""
    enable_javascript: bool = True
    page_load_timeout: int = 30
    implicit_wait: int = 10
    headless: bool = True
    window_size: tuple = (1920, 1080)
    user_agent_rotation: bool = True
    request_delay: float = 1.0
    max_depth: int = 3

@dataclass
class OutputConfig:
    """输出配置"""
    output_format: str = "json"  # json, csv, excel, txt
    output_file: Optional[str] = None
    include_thumbnails: bool = True
    include_metadata: bool = True
    save_to_excel: bool = False
    excel_filename: str = "media_results.xlsx"

@dataclass
class DownloadConfig:
    """下载配置"""
    enable_download: bool = False
    download_path: str = "./downloads"
    create_subdirs: bool = True
    overwrite_existing: bool = False
    max_file_size: int = 1024 * 1024 * 1024  # 1GB
    allowed_types: list = None

@dataclass
class LogConfig:
    """日志配置"""
    log_level: str = "INFO"
    log_file: Optional[str] = None
    console_output: bool = True
    colored_output: bool = True

class Config:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化配置管理器"""
        self.config_file = config_file or "config.json"
        
        # 默认配置
        self.network = NetworkConfig()
        self.crawler = CrawlerConfig()
        self.output = OutputConfig()
        self.download = DownloadConfig()
        self.log = LogConfig()
        
        # 加载配置文件
        self.load_config()
        
        # 设置日志
        self._setup_logging()
    
    def load_config(self) -> None:
        """从配置文件加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新配置
                if 'network' in config_data:
                    self._update_dataclass(self.network, config_data['network'])
                
                if 'crawler' in config_data:
                    self._update_dataclass(self.crawler, config_data['crawler'])
                
                if 'output' in config_data:
                    self._update_dataclass(self.output, config_data['output'])
                
                if 'download' in config_data:
                    self._update_dataclass(self.download, config_data['download'])
                
                if 'log' in config_data:
                    self._update_dataclass(self.log, config_data['log'])
                
                logging.info(f"配置已从 {self.config_file} 加载")
                
            except Exception as e:
                logging.warning(f"加载配置文件失败: {e}，使用默认配置")
        else:
            logging.info("配置文件不存在，使用默认配置")
    
    def save_config(self) -> None:
        """保存配置到文件"""
        try:
            config_data = {
                'network': asdict(self.network),
                'crawler': asdict(self.crawler),
                'output': asdict(self.output),
                'download': asdict(self.download),
                'log': asdict(self.log)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logging.info(f"配置已保存到 {self.config_file}")
            
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
    
    def _update_dataclass(self, obj: Any, data: Dict[str, Any]) -> None:
        """更新数据类对象"""
        for key, value in data.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
    
    def _setup_logging(self) -> None:
        """设置日志"""
        # 设置日志级别
        log_level = getattr(logging, self.log.log_level.upper(), logging.INFO)
        
        # 创建格式器
        if self.log.colored_output:
            try:
                import colorlog
                formatter = colorlog.ColoredFormatter(
                    '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
            except ImportError:
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 控制台输出
        if self.log.console_output:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
        
        # 文件输出
        if self.log.log_file:
            file_handler = logging.FileHandler(self.log.log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
    
    def get_user_agents(self) -> list:
        """获取用户代理列表"""
        return [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
    
    def update_config(self, section: str, **kwargs) -> None:
        """更新配置"""
        if hasattr(self, section):
            config_obj = getattr(self, section)
            for key, value in kwargs.items():
                if hasattr(config_obj, key):
                    setattr(config_obj, key, value)
        
        # 如果更新了日志配置，重新设置日志
        if section == 'log':
            self._setup_logging()

# 全局配置实例
config = Config()
